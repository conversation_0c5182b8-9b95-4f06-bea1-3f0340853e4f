﻿using BFE.Framework.Domain.Core.Attributes;
using BFE.Framework.Domain.Core.Impl;
using System;
using System.Collections.Generic;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg
{
    [AggregateRootName("Projects")]
    public class Project : AggregateRoot
    {
        public Project(string code, string cnName, string createUser, string country)
        {
            Code = code;
            Name = cnName;
            Country = country;
            CreateUser = createUser;
            CreateTime = DateTime.Now;
            IsDelete = false;
        }

        /// <summary>
        /// 项目编号
        /// </summary>
        public string Code { get; private set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; private set; }
        /// <summary>
        /// 国家
        /// </summary>
        public string Country { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; private set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; private set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string ModifyUser { get; private set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; private set; }
        public bool IsDelete { get; private set; }
        public void Edit(string code, string cnName, string modify, string country)
        {
            Code = code;
            Name = cnName;
            Country = country;
            ModifyUser = modify;
            ModifyTime = DateTime.Now;
        }
        public void Delete()
        {
            this.IsDelete = true;
        }
    }
}
