﻿using BFE.Framework.Domain.Core.Attributes;
using BFE.Framework.Domain.Core.Impl;
using System;
using System.Collections.Generic;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg
{
    [AggregateRootName("CrmAgents")]
    public class CrmAgent : AggregateRoot
    {
        public CrmAgent(string code, string cnName, string phone, string email, string leader, string createUser, string contacts, string salesman, string role, string contactType)
        {
            Code = code;
            Name = cnName;
            CreateUser = createUser;
            CreateTime = DateTime.Now;
            IsDelete = false;
            Phone = phone;
            Email = email;
            Leader = leader;
            Contacts = contacts;
            Salesman = salesman;
            Role = role;
            ContactType = contactType;
        }

        /// <summary>
        /// 项目编号
        /// </summary>
        public string Code { get; private set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; private set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string Phone { get; private set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; private set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string Email { get; private set; }
        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; private set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public string Leader { get; private set; }
        /// <summary>
        /// 角色名称
        /// </summary>
        public string Role { get; set; }
        /// <summary>
        /// 联系方式
        /// </summary>
        public string ContactType { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; private set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; private set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string ModifyUser { get; private set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; private set; }
        public bool IsDelete { get; private set; }
        public void Edit(string code, string cnName, string phone, string email, string leader, string modify, string contacts, string salesman, string role, string contactType)
        {
            Code = code;
            Name = cnName;
            Phone = phone;
            Email = email;
            Leader = leader;
            Contacts = contacts;
            ModifyUser = modify;
            Salesman = salesman;
            ModifyTime = DateTime.Now;
            Role = role;
            ContactType = contactType;
        }
        public void Delete()
        {
            this.IsDelete = true;
        }
    }
}
