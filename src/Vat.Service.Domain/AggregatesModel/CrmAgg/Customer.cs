﻿using BFE.Framework.Domain.Core.Attributes;
using BFE.Framework.Domain.Core.Impl;
using System;
using System.Collections.Generic;
using System.Text;
using Vat.Service.Domain.Common.Models;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg
{
    [AggregateRootName("Customers")]
    public class Customer : AggregateRoot
    {
        public Customer(string code, string cnName, string enName, string contacts, string telephone, string email, string source, string type, string createUser, string affiliatedCompany, string contactType = "", string salesman = "", string agent = "", string channel = "", string legalName = "", List<CustomerContact> customerContacts = null)
        {
            Code = code;
            CnName = cnName;
            EnName = enName;
            Contacts = contacts;
            Telephone = telephone;
            Email = email;
            Source = source;
            Type = type;
            CreateUser = createUser;
            CreateTime = DateTime.Now;
            IsDelete = false;
            AffiliatedCompany = affiliatedCompany;
            LegalName = legalName;
            ContactType = contactType;
            Salesman = salesman;
            Agent = agent;
            Channel = channel;
            ContactList = customerContacts;
        }

        /// <summary>
        /// 客户编号
        /// </summary>
        public string Code { get; private set; }
        /// <summary>
        /// 客户中文名称
        /// </summary>
        public string CnName { get; private set; }
        /// <summary>
        /// 客户英文名称
        /// </summary>
        public string EnName { get; private set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; private set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string Telephone { get; private set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string Email { get; private set; }
        /// <summary>
        /// 客户来源
        /// </summary>
        public string Source { get; private set; }
        /// <summary>
        /// 客户类型
        /// </summary>
        public string Type { get; private set; }
        /// <summary>
        /// 联系方式类型
        /// </summary>
        public string ContactType { get; private set; }
        /// <summary>
        /// 维易租户
        /// </summary>
        public string TenantCode { get; private set; }

        /// <summary>
        /// 所属公司
        /// </summary>
        public string AffiliatedCompany { get; private set; }

        /// <summary>
        /// 法人名称
        /// </summary>
        public string LegalName { get; private set; }

        /// <summary>
        /// 关联维易公司id
        /// </summary>
        public string VeCompanyId { get; private set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; private set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; private set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string ModifyUser { get; private set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; private set; }
        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; private set; }
        /// <summary>
        /// 代理
        /// </summary>
        public string Agent { get; private set; }
        /// <summary>
        /// 渠道
        /// </summary>
        public string Channel { get; private set; }
        /// <summary>
        /// 联系人（新）
        /// </summary>
        public List<CustomerContact> ContactList { get; private set; }

        public bool IsDelete { get; private set; }

        public void Edit(string code, string cnName, string enName, string contacts, string telephone, string email, string source, string type, string modifyUser, string affiliatedCompany, string contactType = "", string salesman = "", string agent = "", string channel = "", string legalName = "", List<CustomerContact> customerContacts = null)
        {
            Code = code;
            CnName = cnName;
            EnName = enName;
            Contacts = contacts;
            Telephone = telephone;
            Email = email;
            Source = source;
            Type = type;
            ModifyUser = modifyUser;
            ModifyTime = DateTime.Now;
            AffiliatedCompany = affiliatedCompany;
            LegalName = legalName;
            ContactType = contactType;
            Salesman = salesman;
            Agent = agent;
            Channel = channel;
            ContactList = customerContacts;
        }
        public void Delete()
        {
            this.IsDelete = true; ;
        }
    }
}
