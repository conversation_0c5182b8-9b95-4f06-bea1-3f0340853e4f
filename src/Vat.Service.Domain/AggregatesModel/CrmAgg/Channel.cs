﻿using BFE.Framework.Domain.Core.Attributes;
using BFE.Framework.Domain.Core.Impl;
using System;
using System.Collections.Generic;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg
{
    [AggregateRootName("Channels")]
    public class Channel : AggregateRoot
    {
        public Channel(string code, string companyName, string contacts, string telephone, string email, string address, decimal commissionRate, string remark, string createUser, string salesman, string role, string contactType)
        {
            Code = code;
            CompanyName = companyName;
            Contacts = contacts;
            Telephone = telephone;
            Email = email;
            Address = address;
            CommissionRate = commissionRate;
            Remark = remark;
            CreateUser = createUser;
            Salesman = salesman;
            Role = role;
            ContactType = contactType;
            CreateTime = DateTime.Now;
            IsDelete = false;
        }

        /// <summary>
        /// 编号
        /// </summary>
        public string Code { get; private set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; private set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; private set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string Telephone { get; private set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string Email { get; private set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; private set; }
        /// <summary>
        /// 佣金比例
        /// </summary>
        public decimal CommissionRate { get; private set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; private set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; private set; }
        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; private set; }
        /// <summary>
        /// 角色名称
        /// </summary>
        public string Role { get; set; }
        /// <summary>
        /// 联系方式
        /// </summary>
        public string ContactType { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; private set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string ModifyUser { get; private set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; private set; }
        public bool IsDelete { get; private set; }

        public void Edit(string code, string companyName, string contacts, string telephone, string email, string address, decimal commissionRate, string remark, string modify, string salesman, string role, string contactType)
        {
            Code = code;
            CompanyName = companyName;
            Contacts = contacts;
            Telephone = telephone;
            Email = email;
            Address = address;
            CommissionRate = commissionRate;
            Remark = remark;
            ModifyUser = modify;
            Salesman = salesman;
            Role = role;
            ContactType = contactType;
            ModifyTime = DateTime.Now;
        }
        public void Delete()
        {
            this.IsDelete = true;
        }
    }
}
