﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Domain.Core.Specification;
using System;
using System.Linq.Expressions;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchProductOrderDeliveryByIsDeleteSpecification : Specification<ProductOrderDelivery>
    {
        private readonly bool _isDelete;

        public MatchProductOrderDeliveryByIsDeleteSpecification(bool isDelete)
        {
            _isDelete = isDelete;
        }

        public override Expression<Func<ProductOrderDelivery, bool>> GetExpression()
        {
            return x => x.IsDelete == _isDelete;
        }
    }
}
