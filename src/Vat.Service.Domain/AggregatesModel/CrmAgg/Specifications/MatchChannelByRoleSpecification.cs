﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchChannelByRoleSpecification : Specification<Channel>
    {
        private readonly string _role;

        public MatchChannelByRoleSpecification(string role)
        {
            _role = role;
        }

        public override Expression<Func<Channel, bool>> GetExpression()
        {
            return p => p.Role == _role;
        }
    }
}
