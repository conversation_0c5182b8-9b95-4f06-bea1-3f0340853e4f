using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchCustomerByChannelIdsSpecification : Specification<Customer>
    {
        private readonly IEnumerable<string> _channelIds;

        public MatchCustomerByChannelIdsSpecification(IEnumerable<string> channelIds)
        {
            _channelIds = channelIds;
        }

        public override Expression<Func<Customer, bool>> GetExpression()
        {
            return p => _channelIds.Contains(p.Channel);
        }
    }
}
