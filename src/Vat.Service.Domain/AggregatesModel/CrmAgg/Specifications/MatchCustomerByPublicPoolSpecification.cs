using BFE.Framework.Domain.Core.Specification;
using System;
using System.Linq.Expressions;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    /// <summary>
    /// 根据是否公海客户查询
    /// </summary>
    public class MatchCustomerByPublicPoolSpecification : Specification<Customer>
    {
        private readonly bool _isPublicPool;

        public MatchCustomerByPublicPoolSpecification(bool isPublicPool)
        {
            _isPublicPool = isPublicPool;
        }

        public override Expression<Func<Customer, bool>> GetExpression()
        {
            if (_isPublicPool)
            {
                // 查询公海客户：IsPublicPool == true
                return p => p.IsPublicPool == true;
            }
            else
            {
                // 查询非公海客户：IsPublicPool != true（包括false和历史数据的默认值）
                return p => p.IsPublicPool != true;
            }
        }
    }
}
