﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchProjectByNameSpecification : Specification<Project>
    {
        private readonly string _keyword;

        public MatchProjectByNameSpecification(string keyword)
        {
            _keyword = keyword;
        }

        public override Expression<Func<Project, bool>> GetExpression()
        {
            return p => p.Name.Contains(_keyword);
        }
    }
}
