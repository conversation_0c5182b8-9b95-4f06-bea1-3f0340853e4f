﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchCrmAgentByContactTypeSpecification : Specification<CrmAgent>
    {
        private readonly string _contactType;

        public MatchCrmAgentByContactTypeSpecification(string contactType)
        {
            _contactType = contactType;
        }

        public override Expression<Func<CrmAgent, bool>> GetExpression()
        {
            return p => p.ContactType == _contactType;
        }
    }
}
