﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchCrmAgentByRoleSpecification : Specification<CrmAgent>
    {
        private readonly string _role;

        public MatchCrmAgentByRoleSpecification(string role)
        {
            _role = role;
        }

        public override Expression<Func<CrmAgent, bool>> GetExpression()
        {
            return p => p.Role == _role;
        }
    }
}
