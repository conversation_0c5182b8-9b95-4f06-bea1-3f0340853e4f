﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchChannelByEmailSpecification : Specification<Channel>
    {
        private readonly string _keyword;

        public MatchChannelByEmailSpecification(string keyword)
        {
            _keyword = keyword;
        }

        public override Expression<Func<Channel, bool>> GetExpression()
        {
            return p => p.Email.Contains(_keyword);
        }
    }
}
