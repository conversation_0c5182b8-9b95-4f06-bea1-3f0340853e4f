﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Domain.Core.Specification;
using System;
using System.Linq.Expressions;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchProductOrderDeliveryByProductOrderIdSpecification : Specification<ProductOrderDelivery>
    {
        private readonly string _productOrderId;

        public MatchProductOrderDeliveryByProductOrderIdSpecification(string productOrderId)
        {
            _productOrderId = productOrderId;
        }

        public override Expression<Func<ProductOrderDelivery, bool>> GetExpression()
        {
            return x => x.ProductOrderId == _productOrderId;
        }
    }
}
