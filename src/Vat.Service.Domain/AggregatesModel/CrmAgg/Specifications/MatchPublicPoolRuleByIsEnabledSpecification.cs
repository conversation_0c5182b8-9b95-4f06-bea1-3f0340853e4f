using BFE.Framework.Domain.Core.Specification;
using System;
using System.Linq.Expressions;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    /// <summary>
    /// 根据是否启用查询公海规则
    /// </summary>
    public class MatchPublicPoolRuleByIsEnabledSpecification : Specification<PublicPoolRule>
    {
        private readonly bool _isEnabled;

        public MatchPublicPoolRuleByIsEnabledSpecification(bool isEnabled)
        {
            _isEnabled = isEnabled;
        }

        public override Expression<Func<PublicPoolRule, bool>> GetExpression()
        {
            return p => p.IsEnabled == _isEnabled && !p.IsDelete;
        }
    }
}
