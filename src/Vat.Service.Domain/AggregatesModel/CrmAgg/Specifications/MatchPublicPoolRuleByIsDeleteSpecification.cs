using BFE.Framework.Domain.Core.Specification;
using System;
using System.Linq.Expressions;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    /// <summary>
    /// 根据是否删除查询公海规则
    /// </summary>
    public class MatchPublicPoolRuleByIsDeleteSpecification : Specification<PublicPoolRule>
    {
        private readonly bool _isDelete;

        public MatchPublicPoolRuleByIsDeleteSpecification(bool isDelete)
        {
            _isDelete = isDelete;
        }

        public override Expression<Func<PublicPoolRule, bool>> GetExpression()
        {
            return p => p.IsDelete == _isDelete;
        }
    }
}
