using BFE.Framework.Domain.Core.Specification;
using System;
using System.Linq.Expressions;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    /// <summary>
    /// 根据客户类型排除客户的规范
    /// </summary>
    public class MatchCustomerByNotTypeSpecification : Specification<Customer>
    {
        private readonly string _type;

        public MatchCustomerByNotTypeSpecification(string type)
        {
            _type = type;
        }

        public override Expression<Func<Customer, bool>> GetExpression()
        {
            return customer => customer.Type != _type;
        }
    }
}
