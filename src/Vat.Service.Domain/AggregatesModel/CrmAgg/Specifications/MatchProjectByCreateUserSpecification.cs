﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchProjectByCreateUserSpecification : Specification<Project>
    {
        private readonly string _keyword;

        public MatchProjectByCreateUserSpecification(string keyword)
        {
            _keyword = keyword;
        }

        public override Expression<Func<Project, bool>> GetExpression()
        {
            return p => p.CreateUser == _keyword;
        }
    }
}
