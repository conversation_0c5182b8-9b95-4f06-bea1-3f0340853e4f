﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchChannelByAddressSpecification : Specification<Channel>
    {
        private readonly string _address;

        public MatchChannelByAddressSpecification(string address)
        {
            _address = address;
        }

        public override Expression<Func<Channel, bool>> GetExpression()
        {
            return p => p.Address.Contains(_address);
        }
    }
}
