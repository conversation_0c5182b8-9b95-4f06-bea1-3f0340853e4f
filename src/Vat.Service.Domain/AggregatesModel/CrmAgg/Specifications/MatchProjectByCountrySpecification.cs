﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchProjectByCountrySpecification : Specification<Project>
    {
        private readonly string _country;

        public MatchProjectByCountrySpecification(string country)
        {
            _country = country;
        }

        public override Expression<Func<Project, bool>> GetExpression()
        {
            return p => p.Country == _country;
        }
    }
}
