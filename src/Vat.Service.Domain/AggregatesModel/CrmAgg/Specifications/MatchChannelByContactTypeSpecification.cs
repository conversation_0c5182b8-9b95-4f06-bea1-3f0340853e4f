﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchChannelByContactTypeSpecification : Specification<Channel>
    {
        private readonly string _contactType;

        public MatchChannelByContactTypeSpecification(string contactType)
        {
            _contactType = contactType;
        }

        public override Expression<Func<Channel, bool>> GetExpression()
        {
            return p => p.ContactType == _contactType;
        }
    }
}
