﻿using BFE.Framework.Domain.Core.Specification;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchChannelByCommissionRateSpecification : Specification<Channel>
    {
        private readonly decimal _keyword;

        public MatchChannelByCommissionRateSpecification(decimal keyword)
        {
            _keyword = keyword;
        }

        public override Expression<Func<Channel, bool>> GetExpression()
        {
            return p => p.CommissionRate == _keyword;
        }
    }
}
