﻿using BFE.Framework.Domain.Core.Specification;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchProjectByCreateTimeSpecification : Specification<Project>
    {
        private readonly DateTime _begin;
        private readonly DateTime _end;

        public MatchProjectByCreateTimeSpecification(DateTime begin, DateTime end)
        {
            _begin = begin;
            _end = end;
        }

        public override Expression<Func<Project, bool>> GetExpression()
        {
            return p => p.CreateTime >= _begin && p.CreateTime < _end.AddDays(1);
        }
    }
}
