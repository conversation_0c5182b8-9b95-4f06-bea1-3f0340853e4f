﻿using BFE.Framework.Domain.Core.Attributes;
using BFE.Framework.Domain.Core.Impl;
using System;
using System.Collections.Generic;
using System.Text;
using Vat.Service.Domain.Common.Models;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg
{
    [AggregateRootName("ProductOrderDeliveries")]
    public class ProductOrderDelivery : AggregateRoot
    {
        public ProductOrderDelivery(string productOrderId, DateTime expectedCompletionTime, List<FileItem> deliveryDocuments, string remark, string createUser)
        {
            ProductOrderId = productOrderId;
            ExpectedCompletionTime = expectedCompletionTime;
            DeliveryDocuments = deliveryDocuments;
            Remark = remark;
            CreateUser = createUser;
            CreateTime = DateTime.Now;
            IsDelete = false;
        }

        /// <summary>
        /// 产品订单ID
        /// </summary>
        public string ProductOrderId { get; private set; }

        /// <summary>
        /// 预计完成交付的时间
        /// </summary>
        public DateTime ExpectedCompletionTime { get; private set; }

        /// <summary>
        /// 交付相关的文件
        /// </summary>
        public List<FileItem> DeliveryDocuments { get; private set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remark { get; private set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; private set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; private set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyUser { get; private set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; private set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDelete { get; private set; }

        /// <summary>
        /// 编辑交付记录
        /// </summary>
        public void Edit(DateTime expectedCompletionTime, List<FileItem> deliveryDocuments, string remark, string updateUser)
        {
            ExpectedCompletionTime = expectedCompletionTime;
            DeliveryDocuments = deliveryDocuments;
            Remark = remark;
            ModifyUser = updateUser;
            ModifyTime = DateTime.Now;
        }

        /// <summary>
        /// 删除交付记录
        /// </summary>
        public void Delete()
        {
            this.IsDelete = true;
        }
    }
}
