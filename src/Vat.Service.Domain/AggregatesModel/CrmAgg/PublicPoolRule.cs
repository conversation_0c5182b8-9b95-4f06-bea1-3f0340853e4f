using System;
using BFE.Framework.Domain.Core.Impl;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg
{
    /// <summary>
    /// 公海规则配置
    /// </summary>
    [AggregateRootName("PublicPoolRules")]
    public class PublicPoolRule : AggregateRoot
    {
        public PublicPoolRule(int noFollowUpDays, bool isEnabled, string createUser)
        {
            NoFollowUpDays = noFollowUpDays;
            IsEnabled = isEnabled;
            CreateUser = createUser;
            CreateTime = DateTime.Now;
            IsDelete = false;
        }

        /// <summary>
        /// 无跟进天数阈值
        /// </summary>
        public int NoFollowUpDays { get; private set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; private set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; private set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; private set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyUser { get; private set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; private set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDelete { get; private set; }

        /// <summary>
        /// 编辑规则
        /// </summary>
        public void Edit(int noFollowUpDays, bool isEnabled, string modifyUser)
        {
            NoFollowUpDays = noFollowUpDays;
            IsEnabled = isEnabled;
            ModifyUser = modifyUser;
            ModifyTime = DateTime.Now;
        }

        /// <summary>
        /// 删除规则
        /// </summary>
        public void Delete()
        {
            IsDelete = true;
        }

        /// <summary>
        /// 启用/禁用规则
        /// </summary>
        public void SetEnabled(bool enabled, string modifyUser)
        {
            IsEnabled = enabled;
            ModifyUser = modifyUser;
            ModifyTime = DateTime.Now;
        }
    }
}
