﻿using BFE.Framework.Infrastructure.Data.MongoDB;
using BFE.Framework.Infrastructure.Data.MongoDB.Impl;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Repository.MongoDB.CrmCustomer
{
    public class ProductOrderDeliveryRepository : MongoDBRepository<Domain.AggregatesModel.CrmAgg.ProductOrderDelivery>, IProductOrderDeliveryRepository
    {
        public ProductOrderDeliveryRepository(IMongoDBContext context) : base(context)
        {
        }
    }
}
