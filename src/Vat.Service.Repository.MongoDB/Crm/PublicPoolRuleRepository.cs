using BFE.Framework.Infrastructure.Data.MongoDB;
using BFE.Framework.Infrastructure.Data.MongoDB.Impl;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Repository.MongoDB.CrmCustomer
{
    public class PublicPoolRuleRepository : MongoDBRepository<Domain.AggregatesModel.CrmAgg.PublicPoolRule>, IPublicPoolRuleRepository
    {
        public PublicPoolRuleRepository(IMongoDBContext context) : base(context)
        {
        }
    }
}
