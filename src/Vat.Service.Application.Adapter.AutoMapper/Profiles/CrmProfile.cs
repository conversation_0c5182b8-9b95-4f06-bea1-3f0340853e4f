﻿using AutoMapper;
using BFE.Framework.Infrastructure.Authorization.Developer;
using Vat.Service.Application.ViewModels.Crm;
using Vat.Service.Application.Models.Crm;
using Vat.Service.Domain.AggregatesModel.CrmAgg;

namespace Vat.Service.Application.Adapter.AutoMapper.Profiles
{
    public class CrmProfile : Profile
    {
        public CrmProfile()
        {
            this.CreateMap<CrmCustomer, CrmCustomerModel>();

            this.CreateMap<Customer, CustomerModel>();
            this.CreateMap<Customer, CustomerExportModel>();
            this.CreateMap<Channel, ChannelModel>();
            this.CreateMap<Channel, ChannelExportModel>()
                .ForMember(dst => dst.CommissionRateValue, opt => opt.MapFrom(c => c.CommissionRate == 0 ? "" : c.CommissionRate.ToString() + " %"));
            this.CreateMap<Project, ProjectModel>();
            this.CreateMap<Project, ProjectExportModel>();
            this.CreateMap<ServiceAgreement, ServiceAgreementModel>();
            this.CreateMap<ServiceAgreement, ServiceAgreementExportModel>()
                .ForMember(dst => dst.ContractDate, opt => opt.MapFrom(c => (c.ContractStartDate != null && c.ContractEndDate != null) ? $"{c.ContractStartDate.Value.ToString("yyyy-MM-dd")} - {c.ContractEndDate.Value.ToString("yyyy-MM-dd")}" : ""))
                 .ForMember(dst => dst.ContractAmountValue, opt => opt.MapFrom(c => c.ContractAmount != null ? $"{c.ContractAmount.Amount} {c.ContractAmount.CurrencyCode}" : ""))
                 .ForMember(dst => dst.ReceiveAmountValue, opt => opt.MapFrom(c => c.ReceiveAmount != null ? $"{c.ReceiveAmount.Amount} {c.ReceiveAmount.CurrencyCode}" : ""))
                 .ForMember(dst => dst.CommissionAmountValue, opt => opt.MapFrom(c => c.CommissionAmount != null ? $"{c.CommissionAmount.Amount} {c.CommissionAmount.CurrencyCode}" : ""))
                 .ForMember(dst => dst.PaymentAmountValue, opt => opt.MapFrom(c => c.PaymentAmount != null ? $"{c.PaymentAmount.Amount} {c.PaymentAmount.CurrencyCode}" : ""));
            this.CreateMap<Customer, CustomerItem>();
            this.CreateMap<CrmAgent, CrmAgentModel>();
            this.CreateMap<CrmAgent, CrmAgentExportModel>();
            this.CreateMap<Followup, FollowupModel>();
            this.CreateMap<ProductOrder, ProductOrderModel>();
            this.CreateMap<ProductOrderHandover, ProductOrderHandoverModel>();
            this.CreateMap<ProductOrderDelivery, ViewModels.Crm.ProductOrderDeliveryModel>()
                .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.Remark))
                .ForMember(dest => dest.DeliveryDocument, opt => opt.MapFrom(src => src.DeliveryDocuments));
            this.CreateMap<ProductOrderDelivery, Models.Crm.ProductOrderDeliveryModel>();
            this.CreateMap<Approver, ApproverModel>();
            this.CreateMap<Expense, ExpenseModel>();
            this.CreateMap<CrmPayment, PaymentModel>();
            this.CreateMap<PublicPoolRule, PublicPoolRuleModel>();
        }
    }
}
