﻿using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Vat.Service.Application.DTO.Common;
using Vat.Service.Application.ViewModels.AgentService;
using Vat.Service.Application.ViewModels.Order;
using Vat.Service.Application.ViewModels.Tax;
using Vat.Service.Application.ViewModels.Tax.Request;

namespace Vat.Service.Application.Queries
{
    public interface ITaxNumberQueries
    {
        Task<PageResult<TaxNumberListModel>> GetTaxNumberInPage(TaxNumberParam request);
        Task<TaxNumberModel> GetTaxNumberById(string id);
        Task<PageResult<TaxDeclaredModel>> GetTaxDeclaredList(TaxDeclaredParam param);
        Task<List<CompanyInfoModel>> GetCompanysByName(string name);
        /// <summary>
        /// 根据公司名称模糊查询注册主体
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        Task<List<CompanySimpleModel>> GetSimpleCompanysByName(string name, bool isContainTax);
        /// <summary>
        /// 根据税号模糊查询税号列表
        /// </summary>
        /// <param name="taxno"></param>
        /// <returns></returns>
        Task<List<CompanySimpleModel>> GetSimpleTaxList(string taxno);
        Task<Stream> GetTaxNumberDataPackageByOrderId(List<string> oid);
        Task<Stream> ExportTaxNumbers(TaxNumberParam request);

        Task<Stream> ExportTaxAgentTemplate(ExportTaxAgentTemplateParam request);

        Task<DashboardStatisticModel> DashboardStatistic();

        Task<List<SalesDataDownloadModel>> GetThirdPartyDeclaredOrders(AgentToDeclaredRequest request);
        /// <summary>
        /// 查询购买服务记录
        /// </summary>
        /// <param name="taxid"></param>
        /// <returns></returns>
        Task<List<PaymentModel>> GetPaymentList(string taxid);

        Task<List<CompanyNameModel>> GetCompanyNames(CompanyQueryParam param);

        Task<Stream> ExportTemporaryInfo();
        /// <summary>
        /// 查询税号未完成的待申报
        /// </summary>
        /// <param name="taxid"></param>
        /// <returns></returns>
        Task<List<UnFinishedDeclare>> GetUnFinishedDeclared(string taxid);
        #region 续费提醒
        Task<PageResult<TaxRenewListModel>> GetRenewInPage(TaxRenewParam request);
        #endregion

        /// <summary>
        /// 按用户分组统计产品订单数据
        /// </summary>
        /// <param name="param">统计查询参数</param>
        /// <returns>按用户分组的统计数据</returns>
        Task<List<ProductOrderStatisticsModel>> GetProductOrderStatistics(ProductOrderStatisticsParam param);
    }
}
