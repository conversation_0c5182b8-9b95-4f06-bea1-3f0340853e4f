using BFE.Framework.Domain.Core;
using BFE.Framework.Domain.Core.Paged;
using BFE.Framework.Domain.Core.Specification;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Authorization.Exceptions;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.Extensions.Logging;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Tea.Utils;
using Vat.Service.Application.Common.Model;
using Vat.Service.Application.DTO.Common;
using Vat.Service.Application.ViewModels.Crm;
using Vat.Service.Application.ViewModels.Crm.Request;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications;
using Vat.Service.Application.Models.Crm;
using Vat.Service.Domain.AggregatesModel.ProductAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.TaxAgentAgg.Specifications;
using Vat.Service.Domain.Repositories;
using Vat.Service.Domain.Repositories.TaxAgent;

namespace Vat.Service.Application.Queries.Crm
{
    public class CrmQueries : ICrmQueries
    {
        private readonly ICrmCustomerRepository _crmCustomerRepository;
        private readonly ICustomerRepository _customerRepository;
        private readonly IChannelRepository _channelRepository;
        private readonly IProjectRepository _projectRepository;
        private readonly ICrmAgentRepository _crmAgentRepository;

        private readonly IServiceAgreementRepository _serviceAgreementRepository;
        private readonly ITaxAgentReadOnlyRepository _taxAgentReadOnlyRepository;
        private readonly IFollowupRepository _followupRepository;
        private readonly IProductOrderRepository _productOrderRepository;
        private readonly IProductOrderHandoverRepository _productOrderHandoverRepository;
        private readonly IProductOrderDeliveryRepository _productOrderDeliveryRepository;
        private readonly IApproverRepository _approveRepository;
        private readonly ICrmPaymentRepository _paymentRepository;
        private readonly IExpenseRepository _expenseRepository;

        private readonly ITypeAdapter _typeAdapter;
        private readonly IAdminIdentityService _adminIdentityService;
        private readonly ILogger<CrmQueries> _logger;

        public CrmQueries(ICrmCustomerRepository crmCustomerRepository,
                        ICustomerRepository customerRepository,
                        ITypeAdapter typeAdapter,
                        IChannelRepository channelRepository,
                        IProjectRepository projectRepository,
                        IServiceAgreementRepository serviceAgreementRepository,
                        ITaxAgentReadOnlyRepository taxAgentReadOnlyRepository,
                        ICrmAgentRepository crmAgentRepository,
                        IFollowupRepository followupRepository,
                        IProductOrderRepository productOrderRepository,
                        IProductOrderHandoverRepository productOrderHandoverRepository,
                        IProductOrderDeliveryRepository productOrderDeliveryRepository,
                        IApproverRepository approveRepository,
                        ICrmPaymentRepository paymentRepository,
                        IExpenseRepository expenseRepository,
                        IAdminIdentityService adminIdentityService,
                        ILogger<CrmQueries> logger)
        {
            _crmCustomerRepository = crmCustomerRepository;
            _customerRepository = customerRepository;
            _typeAdapter = typeAdapter;
            _channelRepository = channelRepository;
            _projectRepository = projectRepository;
            _serviceAgreementRepository = serviceAgreementRepository;
            _taxAgentReadOnlyRepository = taxAgentReadOnlyRepository;
            _crmAgentRepository = crmAgentRepository;
            _followupRepository = followupRepository;
            _productOrderRepository = productOrderRepository;
            _productOrderHandoverRepository = productOrderHandoverRepository;
            _productOrderDeliveryRepository = productOrderDeliveryRepository;
            _approveRepository = approveRepository;
            _paymentRepository = paymentRepository;
            _expenseRepository = expenseRepository;
            _adminIdentityService = adminIdentityService;
            _logger = logger;
        }

        #region 客户
        public async Task<PageResult<CustomerModel>> GetCustomerPageList(CustomerParam param)
        {
            var result = new PageResult<CustomerModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            var specification = GetCustomerSpecification(param);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<Customer, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _customerRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<CustomerModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                dataList = _typeAdapter.Adapt<List<CustomerModel>>(dataTmp);
                foreach (var item in dataList)
                {
                    if (!string.IsNullOrEmpty(item.Agent))
                    {
                        var agent = await _crmAgentRepository.GetByKeyAsync(item.Agent);
                        item.AgentName = agent.Name;
                        item.Channel = "";
                        item.ChannelName = "";
                    }
                    else if (!string.IsNullOrEmpty(item.Channel))
                    {
                        var channel = await _channelRepository.GetByKeyAsync(item.Channel);
                        item.ChannelName = channel.Code;
                        item.Agent = "";
                        item.AgentName = "";
                    }
                    else
                    {
                        item.AgentName = "";
                        item.ChannelName = "";
                    }
                }
            }
            result.Data = dataList;
            return result;
        }

        private ISpecification<Customer> GetCustomerSpecification(CustomerParam param)
        {
            ISpecification<Customer> specification = new MatchCustomerByIsDeleteSpecification(false);
            if (!string.IsNullOrEmpty(param.KeyWord))
            {
                specification = specification.And(new MatchCustomerByKeyWordSpecification(param.KeyWord));
            }
            if (param.CheckIds != null && param.CheckIds.Any())
            {
                specification = specification.And(new MatchCustomerByIdsSpecification(param.CheckIds));
            }
            if (!string.IsNullOrEmpty(param.Code))
            {
                specification = specification.And(new MatchCustomerByCodeSpecification(param.Code));
            }
            if (!string.IsNullOrEmpty(param.CnName))
            {
                specification = specification.And(new MatchCustomerByCnNameSpecification(param.CnName));
            }
            if (!string.IsNullOrEmpty(param.EnName))
            {
                specification = specification.And(new MatchCustomerByEnNameSpecification(param.EnName));
            }
            if (!string.IsNullOrEmpty(param.Contacts))
            {
                specification = specification.And(new MatchCustomerByContactsSpecification(param.Contacts));
            }
            if (!string.IsNullOrEmpty(param.Telephone))
            {
                specification = specification.And(new MatchCustomerByTelephoneSpecification(param.Telephone));
            }
            if (!string.IsNullOrEmpty(param.Email))
            {
                specification = specification.And(new MatchCustomerByEmailSpecification(param.Email));
            }
            if (!string.IsNullOrEmpty(param.Source))
            {
                specification = specification.And(new MatchCustomerBySourceSpecification(param.Source));
            }
            if (!string.IsNullOrEmpty(param.Type))
            {
                specification = specification.And(new MatchCustomerByTypeSpecification(param.Type));
            }
            if (!string.IsNullOrEmpty(param.AffiliatedCompany))
            {
                specification = specification.And(new MatchCustomerByAffiliatedSpecification(param.AffiliatedCompany));
            }
            if (!string.IsNullOrEmpty(param.Salesman))
            {
                specification = specification.And(new MatchCustomerBySalesmanSpecification(param.Salesman));
            }
            if (!string.IsNullOrEmpty(param.CreateUser))
            {
                specification = specification.And(new MatchCustomerByCreateUserSpecification(param.CreateUser));
            }
            if (!string.IsNullOrEmpty(param.ModifyUser))
            {
                specification = specification.And(new MatchCustomerByModifyUserSpecification(param.ModifyUser));
            }
            if (!string.IsNullOrEmpty(param.Agent))
            {
                specification = specification.And(new MatchCustomerByAgentSpecification(param.Agent));
            }
            if (!string.IsNullOrEmpty(param.Channel))
            {
                specification = specification.And(new MatchCustomerByChannelSpecification(param.Channel));
            }
            if (param.CreateBeginDate != null && param.CreateEndDate != null)
            {
                specification = specification.And(new MatchCustomerByCreateTimeSpecification(param.CreateBeginDate.Value, param.CreateEndDate.Value));
            }
            return specification;
        }

        public async Task<CustomerModel> GetCustomerDetail(string id)
        {
            var customer = await _customerRepository.GetByKeyAsync(id);
            if (customer == null)
            {
                return null;
            }
            return _typeAdapter.Adapt<CustomerModel>(customer);
        }
        public async Task<List<CustomerItem>> GetCustomerItems(string key)
        {
            if (key.IsNullOrBlank())
            {
                return new List<CustomerItem>();
            }
            var customeritems = await _customerRepository.GetListAsync(new MatchCustomerByIsDeleteSpecification(false)
                .And(new MatchCustomerByKeyWordSpecification(key)));
            if (customeritems == null)
            {
                return new List<CustomerItem>();
            }
            return _typeAdapter.Adapt<List<CustomerItem>>(customeritems);
        }

        public async Task<Stream> ExportCustomer(CustomerParam param)
        {
            var specification = GetCustomerSpecification(param);
            var customerdata = await _customerRepository.GetListAsync(specification);

            var data = new CustomerExportData { Data = _typeAdapter.Adapt<List<CustomerExportModel>>(customerdata) };
            IExportFileByTemplate exporter = new ExcelExporter();
            var path = Directory.GetCurrentDirectory();
            var template = Path.Combine(path, "StaticFiles", $"CustomerExportTemplate.xlsx");

            var bytes = await exporter.ExportBytesByTemplate<CustomerExportData>(data, template);

            var stream = new MemoryStream(bytes);
            return stream;
        }
        #endregion

        #region 渠道
        public async Task<PageResult<ChannelModel>> GetChannelPageList(ChannelParam param)
        {
            var result = new PageResult<ChannelModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            var specification = GetChannelSpecification(param);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<Channel, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _channelRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<ChannelModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                dataList = _typeAdapter.Adapt<List<ChannelModel>>(dataTmp);
            }
            result.Data = dataList;
            return result;
        }

        private ISpecification<Channel> GetChannelSpecification(ChannelParam param)
        {
            ISpecification<Channel> specification = new MatchChannelByIsDeleteSpecification(false);
            if (!string.IsNullOrEmpty(param.KeyWord))
            {
                specification = specification.And(new MatchChannelByKeyWordSpecification(param.KeyWord));
            }
            if (param.CheckIds != null && param.CheckIds.Any())
            {
                specification = specification.And(new MatchChannelByIdsSpecification(param.CheckIds));
            }
            return specification;
        }

        public async Task<ChannelModel> GetChannelDetail(string id)
        {
            var channel = await _channelRepository.GetByKeyAsync(id);
            if (channel == null)
            {
                return null;
            }
            return _typeAdapter.Adapt<ChannelModel>(channel);
        }
        public async Task<List<SelectItem>> GetChannelItems(string key)
        {
            if (key.IsNullOrBlank())
            {
                return new List<SelectItem>();
            }
            var items = await _channelRepository.GetListAsync(new MatchChannelByIsDeleteSpecification(false)
                .And(new MatchChannelByKeyWordSpecification(key)));
            if (items == null)
            {
                return new List<SelectItem>();
            }
            return items.Select(i => new SelectItem { Code = i.Code, Name = i.CompanyName }).ToList();
        }
        public async Task<Stream> ExportChannel(ChannelParam param)
        {
            var specification = GetChannelSpecification(param);
            var channeldata = await _channelRepository.GetListAsync(specification);

            var data = new ChannelExportData { Data = _typeAdapter.Adapt<List<ChannelExportModel>>(channeldata) };
            IExportFileByTemplate exporter = new ExcelExporter();
            var path = Directory.GetCurrentDirectory();
            var template = Path.Combine(path, "StaticFiles", $"ChannelExportTemplate.xlsx");

            var bytes = await exporter.ExportBytesByTemplate<ChannelExportData>(data, template);

            var stream = new MemoryStream(bytes);
            return stream;
        }
        #endregion

        #region 项目
        public async Task<PageResult<ProjectModel>> GetProjectPageList(ProjectParam param)
        {
            var result = new PageResult<ProjectModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            var specification = GetProjectSpecification(param);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<Project, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _projectRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<ProjectModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                dataList = _typeAdapter.Adapt<List<ProjectModel>>(dataTmp);
            }
            result.Data = dataList;
            return result;
        }

        private ISpecification<Project> GetProjectSpecification(ProjectParam param)
        {
            ISpecification<Project> specification = new MatchProjectByIsDeleteSpecification(false);
            if (!string.IsNullOrEmpty(param.KeyWord))
            {
                specification = specification.And(new MatchProjectByKeyWordSpecification(param.KeyWord));
            }
            if (param.CheckIds != null && param.CheckIds.Any())
            {
                specification = specification.And(new MatchProjectByIdsSpecification(param.CheckIds));
            }
            return specification;
        }

        public async Task<ProjectModel> GetProjectDetail(string id)
        {
            var channel = await _projectRepository.GetByKeyAsync(id);
            if (channel == null)
            {
                return null;
            }
            return _typeAdapter.Adapt<ProjectModel>(channel);
        }
        public async Task<List<SelectItem>> GetProjectItems(string key)
        {
            if (key.IsNullOrBlank())
            {
                return new List<SelectItem>();
            }
            var items = await _projectRepository.GetListAsync(new MatchProjectByIsDeleteSpecification(false)
                .And(new MatchProjectByKeyWordSpecification(key)));
            if (items == null)
            {
                return new List<SelectItem>();
            }
            return items.Select(i => new SelectItem { Code = i.Code, Name = i.Name }).ToList();
        }
        public async Task<Stream> ExportProject(ProjectParam param)
        {
            var specification = GetProjectSpecification(param);
            var projectdata = await _projectRepository.GetListAsync(specification);

            var data = new ProjectExportData { Data = _typeAdapter.Adapt<List<ProjectExportModel>>(projectdata) };
            IExportFileByTemplate exporter = new ExcelExporter();
            var path = Directory.GetCurrentDirectory();
            var template = Path.Combine(path, "StaticFiles", $"ProjectExportTemplate.xlsx");

            var bytes = await exporter.ExportBytesByTemplate<ProjectExportData>(data, template);

            var stream = new MemoryStream(bytes);
            return stream;
        }
        #endregion

        #region 服务协议
        public async Task<PageResult<ServiceAgreementModel>> GetServiceAgreementPageList(ServiceAgreementParam param)
        {
            var result = new PageResult<ServiceAgreementModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            var specification = await GetServiceAgreementSpecification(param);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<ServiceAgreement, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _serviceAgreementRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<ServiceAgreementModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                dataList = _typeAdapter.Adapt<List<ServiceAgreementModel>>(dataTmp);
                var channels = await _channelRepository.GetListAsync(new MatchChannelByIsDeleteSpecification(false));
                var projects = await _projectRepository.GetListAsync(new MatchProjectByIsDeleteSpecification(false));
                var agents = await GetAgentItems("");
                foreach (var item in dataList)
                {
                    var customer = await _customerRepository.GetByKeyAsync(item.CustomerId);
                    if (customer == null)
                        continue;
                    item.CustomerCnName = customer.CnName;
                    item.CustomerEnName = customer.EnName;
                    item.CustomerType = customer.Type;
                    item.CustomerCode = customer.Code;
                    item.CustomerSource = customer.Source;
                    item.CustomerContacts = customer.Contacts;
                    item.CustomerEmail = customer.Email;
                    item.AffiliatedCompany = customer.AffiliatedCompany;

                    item.ChannelName = channels.FirstOrDefault(c => c.Code == item.ChannelCode)?.CompanyName;
                    item.ProjectName = projects.FirstOrDefault(c => c.Code == item.ProjectCode || c.Name == item.ProjectCode)?.Name;
                    item.AgentName = agents.FirstOrDefault(c => c.Code == item.Agent)?.Name;
                }
            }
            result.Data = dataList;
            return result;
        }

        private async Task<ISpecification<ServiceAgreement>> GetServiceAgreementSpecification(ServiceAgreementParam param)
        {
            ISpecification<ServiceAgreement> specification = new MatchServiceAgreementByIsDeleteSpecification(false);
            if (!string.IsNullOrEmpty(param.KeyWord))
            {
                ISpecification<Customer> customerSpecification = new MatchCustomerByKeyWordSpecification(param.KeyWord);
                // 查询客户列表
                var customers = await _customerRepository.GetListAsync(customerSpecification);
                if (customers == null)
                {
                    specification = specification.And(new MatchServiceAgreementByKeyWordSpecification(param.KeyWord));
                }
                else
                {
                    var customerList = _typeAdapter.Adapt<List<CustomerItem>>(customers);
                    var customerIds = customers.Select(c => c.Id).ToList();
                    specification = specification.And(new MatchServiceAgreementByCompanyIdSpecification(customerIds));

                }
            }
            if (!string.IsNullOrEmpty(param.Status))
            {
                specification = specification.And(new MatchServiceAgreementByStatusSpecification(param.Status));
            }
            if (!string.IsNullOrEmpty(param.CompanyId))
            {
                specification = specification.And(new MatchServiceAgreementByCompanyIdSpecification(param.CompanyId));
            }
            // 客戶类型
            if (!string.IsNullOrEmpty(param.CustomerType))
            {
                var customerSpecification = new MatchCustomerByTypeSpecification(param.CustomerType);
                // 查询客户列表
                var customers = await _customerRepository.GetListAsync(customerSpecification);
                if (customers != null)
                {
                    var customerList = _typeAdapter.Adapt<List<CustomerItem>>(customers);
                    var customerIds = customers.Select(c => c.Id).ToList();
                    specification = specification.And(new MatchServiceAgreementByCompanyIdSpecification(customerIds));
                }
            }
            // 客戶编码
            if (!string.IsNullOrEmpty(param.CustomerCode))
            {
                var customerSpecification = new MatchCustomerByCodeSpecification(param.CustomerCode);
                // 查询客户列表
                var customers = await _customerRepository.GetListAsync(customerSpecification);
                if (customers != null)
                {
                    var customerList = _typeAdapter.Adapt<List<CustomerItem>>(customers);
                    var customerIds = customers.Select(c => c.Id).ToList();
                    specification = specification.And(new MatchServiceAgreementByCompanyIdSpecification(customerIds));
                }
            }
            // 客户来源
            if (!string.IsNullOrEmpty(param.CustomerSource))
            {
                var customerSpecification = new MatchCustomerBySourceSpecification(param.CustomerSource);
                // 查询客户列表
                var customers = await _customerRepository.GetListAsync(customerSpecification);
                if (customers != null)
                {
                    var customerList = _typeAdapter.Adapt<List<CustomerItem>>(customers);
                    var customerIds = customers.Select(c => c.Id).ToList();
                    specification = specification.And(new MatchServiceAgreementByCompanyIdSpecification(customerIds));
                }
            }
            // 协议编号
            if (!string.IsNullOrEmpty(param.AgreementCode))
            {
                specification = specification.And(new MatchServiceAgreementByAgreementCodeSpecification(param.AgreementCode));
            }
            if (!string.IsNullOrEmpty(param.CountryCode))
            {
                specification = specification.And(new MatchServiceAgreementByCountryCodeSpecification(param.CountryCode));
            }
            if (param.CreateBeginDate != null && param.CreateEndDate != null)
            {
                specification = specification.And(new MatchServiceAgreementBySignDateSpecification(param.CreateBeginDate.Value, param.CreateEndDate.Value));
            }
            if (!string.IsNullOrEmpty(param.Agent))
            {
                specification = specification.And(new MatchServiceAgreementByAgentSpecification(param.Agent));
            }
            if (!string.IsNullOrEmpty(param.ProjectCode))
            {
                specification = specification.And(new MatchServiceAgreementByProjectCodeSpecification(param.ProjectCode));
            }
            // 合同内容
            if (!string.IsNullOrEmpty(param.PartnerCode))
            {
                specification = specification.And(new MatchServiceAgreementByPartnerCodeSpecification(param.PartnerCode));
            }
            // 合同开始日期
            if (param.ContractStartDate != null)
            {
                specification = specification.And(new MatchServiceAgreementByContractStartDateSpecification(param.ContractStartDate.Value));
            }
            // 合同结束日期
            if (param.ContractEndDate != null)
            {
                specification = specification.And(new MatchServiceAgreementByContractEndDateSpecification(param.ContractEndDate.Value));
            }
            // 申报方式
            if (!string.IsNullOrEmpty(param.DeclareMethod))
            {
                specification = specification.And(new MatchServiceAgreementByDeclareMethodSpecification(param.DeclareMethod));
            }
            // 合同金额
            if (!string.IsNullOrEmpty(param.ContractAmount))
            {
                specification = specification.And(new MatchServiceAgreementByContractAmountSpecification(param.ContractAmount));
            }
            // 收款金额
            if (!string.IsNullOrEmpty(param.ReceiveAmount))
            {
                specification = specification.And(new MatchServiceAgreementByReceiveAmountSpecification(param.ReceiveAmount));
            }
            // 计提成本金额
            if (!string.IsNullOrEmpty(param.CommissionAmount))
            {
                specification = specification.And(new MatchServiceAgreementByCommissionAmountSpecification(param.CommissionAmount));
            }
            // 实际付款金额
            if (!string.IsNullOrEmpty(param.PaymentAmount))
            {
                specification = specification.And(new MatchServiceAgreementByPaymentAmountSpecification(param.PaymentAmount));
            }
            if (!string.IsNullOrEmpty(param.CustomerServer))
            {
                specification = specification.And(new MatchServiceAgreementByCustomerServerSpecification(param.CustomerServer));
            }
            if (!string.IsNullOrEmpty(param.Saleman))
            {
                specification = specification.And(new MatchServiceAgreementBySalemanSpecification(param.Saleman));
            }
            if (!string.IsNullOrEmpty(param.TaxManager))
            {
                specification = specification.And(new MatchServiceAgreementByTaxManagerSpecification(param.TaxManager));
            }
            // 合作方编码
            if (!string.IsNullOrEmpty(param.Collaborators))
            {
                specification = specification.And(new MatchServiceAgreementByCollaboratorsSpecification(param.Collaborators));
            }
            // 对应关联到后端算税和维护板块
            if (!string.IsNullOrEmpty(param.Correlation))
            {
                specification = specification.And(new MatchServiceAgreementByCorrelationSpecification(param.Correlation));
            }
            // 创建人
            if (!string.IsNullOrEmpty(param.CreateUser))
            {
                specification = specification.And(new MatchServiceAgreementByCreateUserSpecification(param.CreateUser));
            }
            // 编辑人
            if (!string.IsNullOrEmpty(param.ModifyUser))
            {
                specification = specification.And(new MatchServiceAgreementByModifyUserSpecification(param.ModifyUser));
            }
            if (param.CheckIds != null && param.CheckIds.Any())
            {
                specification = specification.And(new MatchServiceAgreementByIdsSpecification(param.CheckIds));
            }
            return specification;
        }

        public async Task<ServiceAgreementModel> GetServiceAgreementDetail(string id)
        {
            var agreement = await _serviceAgreementRepository.GetByKeyAsync(id);
            if (agreement == null)
            {
                return null;
            }
            var result = _typeAdapter.Adapt<ServiceAgreementModel>(agreement);
            var customer = await _customerRepository.GetByKeyAsync(result.CustomerId);
            if (customer == null)
                return result;
            result.CustomerCnName = customer.CnName;
            result.CustomerEnName = customer.EnName;
            result.CustomerType = customer.Type;
            result.CustomerCode = customer.Code;
            result.AffiliatedCompany = customer.AffiliatedCompany;
            return result;
        }
        /// <summary>
        /// 获取最新的协议编码
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetLastestServiceAgreementCode()
        {
            var agreements = await _serviceAgreementRepository.GetListAsync(new MatchServiceAgreementByIsDeleteSpecification(false)
               .And(new MatchServiceAgreementByTodaySpecification(DateTime.Now)));
            string code = $"XY{DateTime.Now.ToString("yyyyMMdd")}{(agreements.Count() + 1).ToString().PadLeft(4, '0')}";
            return code;
        }

        public async Task<Stream> ExportAgreement(ServiceAgreementParam param)
        {
            var specification = await GetServiceAgreementSpecification(param);
            var agreementdata = await _serviceAgreementRepository.GetListAsync(specification);


            var dataList = _typeAdapter.Adapt<List<ServiceAgreementExportModel>>(agreementdata);
            var channels = await _channelRepository.GetListAsync(new MatchChannelByIsDeleteSpecification(false));
            var projects = await _projectRepository.GetListAsync(new MatchProjectByIsDeleteSpecification(false));
            var agents = await GetAgentItems("");
            var crmagents = await _crmAgentRepository.GetListAsync(new MatchCrmAgentByIsDeleteSpecification(false));
            foreach (var item in dataList)
            {
                var customer = await _customerRepository.GetByKeyAsync(item.CustomerId);
                if (customer == null)
                    continue;
                item.CustomerCnName = customer.CnName;
                item.CustomerEnName = customer.EnName;
                item.CustomerType = customer.Type;
                item.CustomerCode = customer.Code;

                item.ChannelName = channels.FirstOrDefault(c => c.Code == item.ChannelCode)?.CompanyName;
                item.ProjectName = projects.FirstOrDefault(c => c.Code == item.ProjectCode)?.Name;
                item.AgentName = agents.FirstOrDefault(c => c.Code == item.Agent)?.Name;
                item.CrmAgentName = crmagents.FirstOrDefault(c => c.Code == item.CrmAgent)?.Name;
            }

            var data = new ServiceAgreementExportData { Data = dataList };
            IExportFileByTemplate exporter = new ExcelExporter();
            var path = Directory.GetCurrentDirectory();
            var template = Path.Combine(path, "StaticFiles", $"ServiceAgreementExportTemplate.xlsx");

            var bytes = await exporter.ExportBytesByTemplate<ServiceAgreementExportData>(data, template);

            var stream = new MemoryStream(bytes);
            return stream;
        }
        #endregion

        #region crm 代理
        public async Task<PageResult<CrmAgentModel>> GetCrmAgentPageList(CrmAgentParam param)
        {
            var result = new PageResult<CrmAgentModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            var specification = GetCrmAgentSpecification(param);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<CrmAgent, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _crmAgentRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<CrmAgentModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                dataList = _typeAdapter.Adapt<List<CrmAgentModel>>(dataTmp);
            }
            result.Data = dataList;
            return result;
        }

        private ISpecification<CrmAgent> GetCrmAgentSpecification(CrmAgentParam param)
        {
            ISpecification<CrmAgent> specification = new MatchCrmAgentByIsDeleteSpecification(false);
            if (!string.IsNullOrEmpty(param.KeyWord))
            {
                specification = specification.And(new MatchCrmAgentByKeyWordSpecification(param.KeyWord));
            }
            if (param.CheckIds != null && param.CheckIds.Any())
            {
                specification = specification.And(new MatchCrmAgentByIdsSpecification(param.CheckIds));
            }
            return specification;
        }

        public async Task<CrmAgentModel> GetCrmAgentDetail(string id)
        {
            var channel = await _crmAgentRepository.GetByKeyAsync(id);
            if (channel == null)
            {
                return null;
            }
            return _typeAdapter.Adapt<CrmAgentModel>(channel);
        }
        public async Task<List<SelectItem>> GetCrmAgentItems(string key)
        {
            if (key.IsNullOrBlank())
            {
                return new List<SelectItem>();
            }
            var items = await _crmAgentRepository.GetListAsync(new MatchCrmAgentByIsDeleteSpecification(false)
                .And(new MatchCrmAgentByKeyWordSpecification(key)));
            if (items == null)
            {
                return new List<SelectItem>();
            }
            return items.Select(i => new SelectItem { Code = i.Code, Name = i.Name }).ToList();
        }
        public async Task<Stream> ExportCrmAgent(CrmAgentParam param)
        {
            var specification = GetCrmAgentSpecification(param);
            var agentdata = await _crmAgentRepository.GetListAsync(specification);

            var data = new CrmAgentExportData { Data = _typeAdapter.Adapt<List<CrmAgentExportModel>>(agentdata) };
            IExportFileByTemplate exporter = new ExcelExporter();
            var path = Directory.GetCurrentDirectory();
            var template = Path.Combine(path, "StaticFiles", $"CrmAgentExportTemplate.xlsx");

            var bytes = await exporter.ExportBytesByTemplate<CrmAgentExportData>(data, template);

            var stream = new MemoryStream(bytes);
            return stream;
        }
        #endregion

        /// <summary>
        /// 获取税代的下拉列表框
        /// </summary>
        /// <returns></returns>
        public async Task<List<SelectItem>> GetAgentItems(string key)
        {
            var items = await _taxAgentReadOnlyRepository.GetListAsync(new MatchTaxAgentByIsDeleteSpecification(false));
            if (key.IsNullOrBlank())
            {
                return items.Select(i => new SelectItem { Code = i.TaxAgentId, Name = i.TaxAgentName }).ToList();
            }

            return items.Where(i => i.TaxAgentName.Contains(key)).Select(i => new SelectItem { Code = i.TaxAgentId, Name = i.TaxAgentName }).ToList();
        }

        public async Task<PageResult<CrmCustomerModel>> GetCrmCustomerPageList(CrmCustomerParam param)
        {
            var result = new PageResult<CrmCustomerModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            var specification = GetSpecification(param);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<CrmCustomer, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _crmCustomerRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<CrmCustomerModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                dataList = _typeAdapter.Adapt<List<CrmCustomerModel>>(dataTmp);
            }
            result.Data = dataList;
            return result;
        }

        public async Task<CrmCustomerModel> GetCrmCustomerDetail(string id)
        {
            var CrmCustomer = await _crmCustomerRepository.GetByKeyAsync(id);
            if (CrmCustomer == null)
            {
                return null;
            }
            return _typeAdapter.Adapt<CrmCustomerModel>(CrmCustomer);
        }


        private ISpecification<CrmCustomer> GetSpecification(CrmCustomerParam param)
        {
            ISpecification<CrmCustomer> specification = new MatchCrmCustomerByIsDeleteSpecification(false);
            if (!string.IsNullOrEmpty(param.KeyWord))
            {
                specification = specification.And(new MatchCrmCustomerByKeyWordSpecification(param.KeyWord));
            }
            if (!string.IsNullOrEmpty(param.CustomerCode))
            {
                specification = specification.And(new MatchCrmCustomerByCustomerCodeSpecification(param.CustomerCode));
            }


            return specification;
        }

        #region 跟进记录
        public async Task<PageResult<FollowupModel>> GetFollowupPageList(FollowupParam param)
        {
            var result = new PageResult<FollowupModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            ISpecification<Followup> specification = new MatchFollowupByIsDeleteSpecification(false);
            if (!string.IsNullOrEmpty(param.CustomerId))
            {
                specification = specification.And(new MatchFollowupByCustomerIdSpecification(param.CustomerId));
            }

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<Followup, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _followupRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<FollowupModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                dataList = _typeAdapter.Adapt<List<FollowupModel>>(dataTmp);
            }
            result.Data = dataList;
            return result;
        }

        #endregion

        #region 产品订单
        public async Task<PageResult<ProductOrderModel>> GetProductOrderPageList(ProductOrderParam param)
        {
            var result = new PageResult<ProductOrderModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            ISpecification<ProductOrder> specification = new MatchProductOrderByIsDeleteSpecification(false);
            var customerIds = new List<string>();
            var hasCustomer = false;
            if (!string.IsNullOrEmpty(param.CustomerCode))
            {
                hasCustomer = true;
                // 根据客户code查询客户ID，再根据客户ID查询产品订单
                var customers = await _customerRepository.GetListAsync(new MatchCustomerByIsDeleteSpecification(false).And(new MatchCustomerByKeyWordSpecification(param.CustomerCode)));
                var customerList = _typeAdapter.Adapt<List<CustomerItem>>(customers);
                customerIds = customerList.Select(x => x.Id).ToList();
            }
            if (!string.IsNullOrEmpty(param.CustomerName))
            {
                hasCustomer = true;
                // 根据客户name查询客户ID，再根据客户ID查询产品订单
                var customers = await _customerRepository.GetListAsync(new MatchCustomerByIsDeleteSpecification(false).And(new MatchCustomerByKeyWordSpecification(param.CustomerName)));
                var customerList = _typeAdapter.Adapt<List<CustomerItem>>(customers);
                customerIds.AddRange(customerList.Select(x => x.Id).ToList());
            }
            if (hasCustomer)
            {
                customerIds = customerIds.Distinct().ToList();
                specification = specification.And(new MatchProductOrderByCustomerIdsSpecification(customerIds));
            }
            if (!string.IsNullOrEmpty(param.Status))
            {
                specification = specification.And(new MatchProductOrderByStatusSpecification(param.Status));
            }

            // 判断当前用户是否有查看所有产品订单的权限
            var user = _adminIdentityService.GetAdminUserIdentity();
            _logger.LogError($"当前用户：{user.UserName}");
            var userId = _adminIdentityService.GetAdminUserId();
            _logger.LogError($"当前用户Id：{userId}");
            var permission = await _adminIdentityService.GetUserPermissionsAsync(userId);
            // _logger.LogError($"当前用户权限：{(permission != null ? string.Join("、", permission.Permissions) : "")}");
            if (permission != null)
            {
                if (!permission.Permissions.Contains("Allow_ViewAllProductOrder"))
                {
                    specification = specification.And(new MatchProductOrderByUserNameSpecification(user.UserName));
                }
            }
            else
            {
                specification = specification.And(new MatchProductOrderByUserNameSpecification(user.UserName));
            }
            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<ProductOrder, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _productOrderRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<ProductOrderModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                // 获取对应的服务协议列表
                var serviceAgreementIds = dataTmp.Select(x => x.ServiceAgreementId).Distinct().ToList();
                var serviceAgreementList = await _serviceAgreementRepository.GetListAsync(new MatchServiceAgreementByIsDeleteSpecification(false).And(new MatchServiceAgreementByIdsSpecification(serviceAgreementIds)));
                foreach (var item in dataTmp)
                {
                    var id = item.Id;
                    var status = item.Status;
                    var serviceAgreementId = item.ServiceAgreementId;
                    var serviceAgreement = serviceAgreementList.FirstOrDefault(p => p.Id == serviceAgreementId);
                    var serviceAgreementViewModel = _typeAdapter.Adapt<ServiceAgreementModel>(serviceAgreement);
                    // 从服务协议中提取对应的产品
                    ServiceProject product = null;
                    if (serviceAgreementViewModel != null && serviceAgreementViewModel.Projects != null)
                    {
                        product = serviceAgreementViewModel.Projects.FirstOrDefault(p => p.Code == item.ProductCode);
                    }

                    // 客户信息
                    var customer = await this.GetCustomerDetail(item.CustomerId);
                    var productOrder = _typeAdapter.Adapt<ProductOrderModel>(item);
                    productOrder.ServiceAgreement = serviceAgreementViewModel;
                    productOrder.Product = product;
                    productOrder.Customer = customer;
                    dataList.Add(productOrder);
                }
            }
            result.Data = dataList;
            return result;
        }

        public async Task<ProductOrderModel> GetProductOrderDetail(string id)
        {
            var productOrder = await _productOrderRepository.GetByKeyAsync(id);
            if (productOrder == null)
            {
                return null;
            }
            //查找对应的服务协议
            var serviceAgreement = await _serviceAgreementRepository.GetByKeyAsync(productOrder.ServiceAgreementId);
            var serviceAgreementViewModel = _typeAdapter.Adapt<ServiceAgreementModel>(serviceAgreement);
            // 提取服务项目中的产品
            ServiceProject product = null;
            if (serviceAgreementViewModel != null && serviceAgreementViewModel.Projects != null)
            {
                product = serviceAgreementViewModel.Projects.FirstOrDefault(p => p.Code == productOrder.ProductCode);
            }
            // 客户信息
            var customer = await this.GetCustomerDetail(productOrder.CustomerId);

            var productOrderModel = _typeAdapter.Adapt<ProductOrderModel>(productOrder);
            productOrderModel.ServiceAgreement = serviceAgreementViewModel;
            productOrderModel.Product = product;
            productOrderModel.Customer = customer;
            return productOrderModel;
        }

        #endregion

        #region 产品订单交接记录
        public async Task<List<ProductOrderHandoverModel>> GetProductOrderHandoverList(string produceOrderId)
        {
            var result = new List<ProductOrderHandoverModel>();
            if (produceOrderId == null)
            {
                return result;
            }
            ISpecification<ProductOrderHandover> specification = new MatchProductOrderHandoverByIsDeleteSpecification(false);
            specification = specification.And(new MatchProductOrderHandoverByProductOrderIdSpecification(produceOrderId));

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<ProductOrderHandover, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);
            var list = await _productOrderHandoverRepository.GetListAsync(specification, sort);
            result = _typeAdapter.Adapt<List<ProductOrderHandoverModel>>(list.ToList());
            return result;
        }
        #endregion

        #region 产品订单交付记录
        /// <summary>
        /// 获取产品订单交付记录列表
        /// </summary>
        public async Task<List<Models.Crm.ProductOrderDeliveryModel>> GetProductOrderDeliveryList(string productOrderId)
        {
            var result = new List<Models.Crm.ProductOrderDeliveryModel>();
            if (productOrderId == null)
            {
                return result;
            }
            ISpecification<ProductOrderDelivery> specification = new MatchProductOrderDeliveryByIsDeleteSpecification(false);
            specification = specification.And(new MatchProductOrderDeliveryByProductOrderIdSpecification(productOrderId));

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<ProductOrderDelivery, dynamic>>, SortOrder>();
            var sortOrder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortOrder);
            var list = await _productOrderDeliveryRepository.GetListAsync(specification, sort);
            result = _typeAdapter.Adapt<List<Models.Crm.ProductOrderDeliveryModel>>(list.ToList());
            return result;
        }
        #endregion

        #region 审核人
        public async Task<List<ApproverModel>> GetApproverList()
        {
            var list = await _approveRepository.GetAllAsync();
            var result = _typeAdapter.Adapt<List<ApproverModel>>(list.ToList());
            return result;
        }

        #endregion

        #region 收款
        public async Task<PageResult<PaymentModel>> GetPaymentPageList(PaymentParam param)
        {
            var result = new PageResult<PaymentModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            ISpecification<CrmPayment> specification = new MatchPaymentByIsDeleteSpecification(false);
            if (!string.IsNullOrEmpty(param.CustomerName))
            {
                specification = specification.And(new MatchPaymentByCustomerNameSpecification(param.CustomerName));
            }
            if (!string.IsNullOrEmpty(param.CustomerCode))
            {
                specification = specification.And(new MatchPaymentByCustomerCodeSpecification(param.CustomerCode));
            }
            if (!string.IsNullOrEmpty(param.Status))
            {
                specification = specification.And(new MatchPaymentByStatusSpecification(param.Status));
            }
            if (!string.IsNullOrEmpty(param.ReceivingAccount))
            {
                specification = specification.And(new MatchPaymentByReceivingAccountSpecification(param.ReceivingAccount));
            }
            if (!string.IsNullOrEmpty(param.AgreementCode))
            {
                specification = specification.And(new MatchPaymentByAgreementCodeSpecification(param.AgreementCode));
            }
            var user = _adminIdentityService.GetAdminUserIdentity();

            specification = specification.And(new MatchPaymentByUserNameSpecification(user.UserName));
            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<CrmPayment, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _paymentRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<PaymentModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                foreach (var item in dataTmp)
                {
                    var payment = _typeAdapter.Adapt<PaymentModel>(item);
                    var isPass = item.Auditors.All(item => item.Status == "审核通过");
                    var status = isPass ? "审核通过" : "待审核";
                    payment.Status = status;
                    dataList.Add(payment);
                }
            }
            result.Data = dataList;
            return result;
        }

        public async Task<PaymentModel> GetPaymentDetail(string id)
        {
            var payment = await _paymentRepository.GetByKeyAsync(id);
            if (payment == null)
            {
                return null;
            }

            var result = _typeAdapter.Adapt<PaymentModel>(payment);
            var isPass = payment.Auditors.All(item => item.Status == "审核通过");
            var status = isPass ? "审核通过" : "待审核";
            result.Status = status;
            return result;
        }

        #endregion

        #region 支出
        public async Task<PageResult<ExpenseModel>> GetExpensePageList(ExpenseParam param)
        {
            var result = new PageResult<ExpenseModel>();
            result.PageNumber = param.PageNumber;
            result.PageSize = param.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            ISpecification<Expense> specification = new MatchExpenseByIsDeleteSpecification(false);
            if (!string.IsNullOrEmpty(param.CustomerName))
            {
                specification = specification.And(new MatchExpenseByCustomerNameSpecification(param.CustomerName));
            }
            if (!string.IsNullOrEmpty(param.CustomerCode))
            {
                specification = specification.And(new MatchExpenseByCustomerCodeSpecification(param.CustomerCode));
            }
            if (!string.IsNullOrEmpty(param.Status))
            {
                specification = specification.And(new MatchExpenseByStatusSpecification(param.Status));
            }
            if (!string.IsNullOrEmpty(param.ExpenseAccount))
            {
                specification = specification.And(new MatchExpenseByReceivingAccountSpecification(param.ExpenseAccount));
            }
            var user = _adminIdentityService.GetAdminUserIdentity();

            specification = specification.And(new MatchExpenseByUserNameSpecification(user.UserName));
            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<Expense, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            sort.Add(p => p.CreateTime, sortorder);

            var query = await _expenseRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);
            result.PageNumber = query.PageNumber;
            result.PageSize = query.PageSize;
            result.TotalPages = query.TotalPages;
            result.TotalRecords = query.TotalRecords;

            var dataList = new List<ExpenseModel>();
            if (query.Data.Count > 0)
            {
                var dataTmp = query.Data;
                foreach (var item in dataTmp)
                {
                    var payment = _typeAdapter.Adapt<ExpenseModel>(item);
                    var isPass = item.Auditors.All(item => item.Status == "审核通过");
                    var status = isPass ? "审核通过" : "待审核";
                    payment.Status = status;
                    dataList.Add(payment);
                }
            }
            result.Data = dataList;
            return result;
        }

        public async Task<ExpenseModel> GetExpenseDetail(string id)
        {
            var expense = await _expenseRepository.GetByKeyAsync(id);
            if (expense == null)
            {
                return null;
            }

            var result = _typeAdapter.Adapt<ExpenseModel>(expense);
            var isPass = expense.Auditors.All(item => item.Status == "审核通过");
            var status = isPass ? "审核通过" : "待审核";
            result.Status = status;
            return result;
        }

        #endregion
    }
}
