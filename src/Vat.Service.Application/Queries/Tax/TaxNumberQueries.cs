using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BFE.Framework.Domain.Core.Paged;
using BFE.Framework.Domain.Core.Specification;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.Extensions.Logging;
using Vat.Service.Application.Common.Attributes;
using Vat.Service.Application.Common.Helpers;
using Vat.Service.Application.Common.Methods;
using Vat.Service.Application.Constants;
using Vat.Service.Application.DTO.Common;
using Vat.Service.Application.Extensions;
using Vat.Service.Application.ViewModels.AgentService;
using Vat.Service.Application.ViewModels.Order;
using Vat.Service.Application.ViewModels.Tax;
using Vat.Service.Application.ViewModels.Tax.Request;
using Vat.Service.Application.ViewModels.TaxAgent;
using Vat.Service.Application.ViewModles.Common;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.AggregatesModel.IOSSOrderAgg;
using Vat.Service.Domain.AggregatesModel.IOSSOrderAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.OrderAgg;
using Vat.Service.Domain.AggregatesModel.OrderAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.OtherServiceOrderAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.ProductAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.TaxAgg;
using Vat.Service.Domain.AggregatesModel.TaxAgg.Specifications;
using Vat.Service.Domain.Constants.Status;
using Vat.Service.Domain.Constants.Type;
using Vat.Service.Domain.Repositories;
using Vat.Service.Domain.Repositories.IOSSOrder;
using Vat.Service.Domain.Repositories.Order;
using Vat.Service.Domain.Repositories.OtherServiceOrder;
using Vat.Service.Domain.Repositories.Prodcut;
using Vat.Service.Domain.Repositories.Tax;
using Vat.Service.Domain.Repositories.VatDeclare;
using Vat.Service.Infastructure.Exception;

namespace Vat.Service.Application.Queries.Tax
{
    public class TaxNumberQueries : ITaxNumberQueries
    {
        private readonly ITaxNumberRepository _taxNumberRepository;
        private readonly ITypeAdapter _typeAdapter = null;
        private readonly IAdminIdentityService _adminIdentityService = null;
        private readonly ICompanyInfoReadOnlyRepository _companyInfoReadOnlyRepository;
        private readonly IVatDeclareReadOnlyRepository _vatDeclareReadOnlyRepository;
        private readonly IRegisterOrderRepository _registerOrderRepository;
        private readonly FileServiceMethod _fileServiceMethod = null;
        private readonly ITaxAgentQueries _taxAgentQueries;
        private readonly IDeclaredOrderRepository _declaredOrderRepository;
        private readonly IOtherServiceOrderRepository _otherServiceOrderRepository;
        private readonly IVatNationalBusinessQueries _nationalBusinessQueries;
        private readonly ILogger<TaxNumberQueries> _logger;
        private readonly IPaymentRepository _paymentRepository;
        private readonly IIOSSRegisterOrderRepository _iossRegisterOrderRepository;
        private readonly IIOSSDeclaredOrderRepository _iossdeclaredOrderRepository;
        private readonly IProductReadOnlyRepository _productRepository;
        private readonly IDeclaredOrderReadOnlyRepository _declaredOrderReadOnlyRepository;
        private readonly IIOSSDeclaredOrderReadOnlyRepository _iossdeclaredOrderReadOnlyRepository;
        private readonly IProductOrderRepository _productOrderRepository;
        private readonly ICustomerRepository _customerRepository;
        private readonly ICrmPaymentRepository _crmPaymentRepository;
        private readonly IExpenseRepository _expenseRepository;
        private readonly IFollowupRepository _followupRepository;

        public TaxNumberQueries(ITaxNumberRepository taxNumberRepository, ITypeAdapter typeAdapter, IAdminIdentityService adminIdentityService,
            ICompanyInfoReadOnlyRepository companyInfoReadOnlyRepository, IVatDeclareReadOnlyRepository vatDeclareReadOnlyRepository,
            IRegisterOrderRepository registerOrderRepository, FileServiceMethod fileServiceMethod, ITaxAgentQueries taxAgentQueries,
            IDeclaredOrderRepository declaredOrderRepository, IOtherServiceOrderRepository otherServiceOrderRepository,
            IVatNationalBusinessQueries nationalBusinessQueries, ILogger<TaxNumberQueries> logger, IPaymentRepository paymentRepository,
            IIOSSRegisterOrderRepository iossRegisterOrderRepository, IIOSSDeclaredOrderRepository iossdeclaredOrderRepository,
            IProductReadOnlyRepository productReadOnlyRepository, IDeclaredOrderReadOnlyRepository declaredOrderReadOnlyRepository,
            IIOSSDeclaredOrderReadOnlyRepository iossdeclaredOrderReadOnlyRepository, IProductOrderRepository productOrderRepository,
            ICustomerRepository customerRepository, ICrmPaymentRepository crmPaymentRepository, IExpenseRepository expenseRepository,
            IFollowupRepository followupRepository)
        {
            _taxNumberRepository = taxNumberRepository;
            _typeAdapter = typeAdapter;
            _adminIdentityService = adminIdentityService;
            _companyInfoReadOnlyRepository = companyInfoReadOnlyRepository;
            _vatDeclareReadOnlyRepository = vatDeclareReadOnlyRepository;
            _registerOrderRepository = registerOrderRepository;
            _fileServiceMethod = fileServiceMethod;
            _taxAgentQueries = taxAgentQueries;
            _otherServiceOrderRepository = otherServiceOrderRepository;
            _declaredOrderRepository = declaredOrderRepository;
            _nationalBusinessQueries = nationalBusinessQueries;
            _logger = logger;
            _paymentRepository = paymentRepository;
            _iossRegisterOrderRepository = iossRegisterOrderRepository;
            _iossdeclaredOrderRepository = iossdeclaredOrderRepository;
            _productRepository = productReadOnlyRepository;
            _declaredOrderReadOnlyRepository = declaredOrderReadOnlyRepository;
            _iossdeclaredOrderReadOnlyRepository = iossdeclaredOrderReadOnlyRepository;
            _productOrderRepository = productOrderRepository;
            _customerRepository = customerRepository;
            _crmPaymentRepository = crmPaymentRepository;
            _expenseRepository = expenseRepository;
            _followupRepository = followupRepository;
        }

        public async Task<PageResult<TaxNumberListModel>> GetTaxNumberInPage(TaxNumberParam request)
        {
            var result = new PageResult<TaxNumberListModel>();
            result.PageNumber = request.PageNumber;
            result.PageSize = request.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            ISpecification<TaxNumber> specification = GetSpecification(request);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<TaxNumber, dynamic>>, SortOrder>();
            sort.Add(p => p.CreateTime, SortOrder.Descending);

            var pageResult = await _taxNumberRepository.FindInPageAsync(request.PageNumber, request.PageSize, specification, sort);

            result.PageNumber = pageResult.PageNumber;
            result.PageSize = pageResult.PageSize;
            result.TotalPages = pageResult.TotalPages;
            result.TotalRecords = pageResult.TotalRecords;

            var dataList = new List<TaxNumberListModel>();
            if (pageResult.Data.Count > 0)
            {
                var dataTmp = pageResult.Data;
                dataList = _typeAdapter.Adapt<List<TaxNumberListModel>>(dataTmp);

                #region 国家配置信息
                var nationConfigs = await _nationalBusinessQueries.GetAllNationBusiness();
                #endregion
                foreach (var item in dataList)
                {
                    item.SupportTaxNumberPause = item.TaxType == "IOSS" ? true : nationConfigs.First(c => c.CountryCode == item.TaxCountry.Code).OtherBusinessFields.Select(c => c.Value).Contains("TaxNumberPause");
                    item.SupportUnsubscribe = nationConfigs.First(c => c.CountryCode == item.TaxCountry.Code).OtherBusinessFields.Select(c => c.Value).Contains("Unsubscribe");
                }
            }
            result.Data = dataList;
            return result;
        }

        private ISpecification<TaxNumber> GetSpecification(TaxNumberParam request, bool isExport = false)
        {
            var userInfo = _adminIdentityService.GetAdminUserIdentity();

            ISpecification<TaxNumber> specification = new MatchTaxNumerByIsDeletedSpecification(false);

            var groupsId = userInfo.Groups.ToList();
            ////根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            if (groupsId.All(x => x != 1) && groupsId.All(x => x != 112) && groupsId.All(x => x != 113) && groupsId.All(x => x != 114))
            {
                specification = specification.And(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));
            }
            if (!request.KeyWords.IsNullOrBlank())
            {//关键字
             //specification = specification.And(new MatchTaxNumberByTaxNoSpecification(request.KeyWords.Trim()));
                specification = specification.And(new MatchTaxNumberByKeyWordSpecification(request.KeyWords.Trim()));
            }
            if (!request.VatCountryCode.IsNullOrBlank())
            {
                specification = specification.And(new MatchTaxNumberByVatCountrySpecification(request.VatCountryCode));
            }
            if (request.Emails != null && request.Emails.Any())
            {//Email
                //specification = specification.And(new MatchTaxNumberByTenantEmailListSpecification(request.Emails));
                specification = specification.And(new MatchTaxNumberByTenantCodeListSpecification(request.Emails));
            }
            if (!request.Status.IsNullOrBlank())
            {
                specification = specification.And(new MatchTaxNumberByStatusSpecification(request.Status));
            }
            if (!request.RegisterMain.IsNullOrBlank())
            {//注册主体
                specification = specification.And(new MatchTaxNumberByRegisterMainSpecification(request.RegisterMain.Trim()));
            }
            if (!string.IsNullOrEmpty(request.BusinessType))
            {
                specification = specification.And(new MatchTaxNumberByBusinessTypeSpecification(request.BusinessType));
            }

            if (request.SaleManager != "all" && request.SaleManager != null && !isExport)
            {
                specification = specification.And(new MatchTaxNumberByManagerSpecification(request.SaleManager, ManagerType.SaleManager));
            }
            if (request.CustomerManager != "all" && request.CustomerManager != null && !isExport)
            {
                specification = specification.And(new MatchTaxNumberByManagerSpecification(request.CustomerManager, ManagerType.CustomerManager));
            }

            if (request.Ids != null && request.Ids.Any())
            {
                specification = specification.And(new MatchTaxNumerByIdsSpecification(request.Ids));
            }
            if (!request.TaxAgent.IsNullOrBlank())
            {//税务代理
                specification = specification.And(new MatchTaxNumberByTaxAgentIdSpecification(request.TaxAgent));
            }
            if (request.IsTester != null)
            {
                specification = specification.And(new MatchTaxNumberByIsTesterSpecification(request.IsTester.Value));
            }
            if (request.DeclaredDeadline != null)
            {//申报服务截止日期
                specification = specification.And(new MatchTaxNumberByDeclaredDeadlineSpecification(request.DeclaredDeadline.Value.AddDays(1)));
            }
            if (request.DeclaredAmount != null)
            {//已申报次数
                specification = specification.And(new MatchTaxNumberByDeclaredAmountSpecification(request.DeclaredAmount.Value));
            }
            return specification;
        }

        /// <summary>
        /// 查询订单详情
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<TaxNumberModel> GetTaxNumberById(string id)
        {
            //ISpecification<TaxNumber> specification = new MatchTaxNumerByIsDeletedSpecification(false);
            var userInfo = _adminIdentityService.GetAdminUserIdentity();
            var groupsId = userInfo.Groups;
            ////根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            //if (groupsId.All(x => x != 1) && groupsId.All(x => x != 112) && groupsId.All(x => x != 113) && groupsId.All(x => x != 114))
            //{
            //    specification = new MatchVatRegisterByClientManagerSpecification(userInfo.UserName);
            //}
            //specification = specification.And(new MatchTaxNumberByOrderIdSpecification(orderid));
            var taxnumber = await _taxNumberRepository.GetByKeyAsync(id);
            //日志查询
            //var vatLog = await _vatLogRemarkReadOnlyRepository.GetListAsync(new MatchVatLogReMarkByOrderIdSpecification(orderid));
            var result = new TaxNumberModel();
            if (taxnumber != null)
            {
                result = _typeAdapter.Adapt<TaxNumberModel>(taxnumber);
                if (taxnumber.TaxType == TaxType.VAT)
                {
                    var taxno = taxnumber.GetDeclaredObjectValue();
                    var declares = await _declaredOrderRepository.GetListAsync(new MatchDeclaredOrderByTenantSpecification(taxnumber.Tenant.Code)
                        .And(new MatchDeclaredOrderByIsDeleteSpecification(false))
                       .And(new MatchDeclaredOrderByTaxNoSpecification(taxno)));
                    result.HasDeclared = declares.Any();
                    declares = declares.Where(d => d.Status == DeclaredOrderStatus.Finished &&
                    d.DeclarationCycle.StartTime >= new DateTime(DateTime.Now.Year, 1, 1).ConvertToUtc() && d.DeclarationCycle.EndTime <= DateTime.UtcNow).ToList();
                    if (declares != null)
                    {
                        result.TaxDeclares = _typeAdapter.Adapt<List<TaxDeclareModel>>(declares);
                    }
                }
                else if (taxnumber.TaxType == TaxType.IOSS)
                {
                    var taxno = taxnumber.IOSSCode;
                    var declares = await _iossdeclaredOrderRepository.GetListAsync(new MatchIOSSDeclaredOrderByTenantCodeSpecification(taxnumber.Tenant.Code)
                        .And(new MatchIOSSDeclaredOrderByIsDeleteSpecification(false))
                       .And(new MatchIOSSDeclaredOrderByIOSSCodeSpecification(taxno)));
                    result.HasDeclared = declares.Any();
                    declares = declares.Where(d => d.Status == DeclaredOrderStatus.Finished &&
                    d.DeclarationCycle.StartTime >= new DateTime(DateTime.Now.Year, 1, 1).ConvertToUtc() && d.DeclarationCycle.EndTime <= DateTime.UtcNow).ToList();
                    if (declares != null)
                    {
                        result.TaxDeclares = _typeAdapter.Adapt<List<TaxDeclareModel>>(declares);
                    }
                }
            }

            return result;
        }
        public async Task<PageResult<TaxDeclaredModel>> GetTaxDeclaredList(TaxDeclaredParam param)
        {
            var result = new PageResult<TaxDeclaredModel>();
            var taxnumber = await _taxNumberRepository.GetByKeyAsync(param.TaxId);
            if (taxnumber.TaxType == TaxType.VAT)
            {
                ISpecification<DeclaredOrder> specification = new MatchDeclaredOrderByTenantSpecification(taxnumber.Tenant.Code)
                           .And(new MatchDeclaredOrderByIsDeleteSpecification(false))
                          .And(new MatchDeclaredOrderByTaxNoSpecification(taxnumber.GetDeclaredObjectValue()));
                var sort = new Dictionary<System.Linq.Expressions.Expression<Func<DeclaredOrder, dynamic>>, SortOrder>();
                sort.Add(p => p.CreateTime, SortOrder.Descending);
                var pageResult = await _declaredOrderRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);

                result.PageNumber = pageResult.PageNumber;
                result.PageSize = pageResult.PageSize;
                result.TotalPages = pageResult.TotalPages;
                result.TotalRecords = pageResult.TotalRecords;
                result.Data = _typeAdapter.Adapt<List<TaxDeclaredModel>>(pageResult.Data);
            }
            else if (taxnumber.TaxType == TaxType.IOSS)
            {
                ISpecification<IOSSDeclaredOrder> specification = new MatchIOSSDeclaredOrderByTenantCodeSpecification(taxnumber.Tenant.Code)
                           .And(new MatchIOSSDeclaredOrderByIsDeleteSpecification(false))
                          .And(new MatchIOSSDeclaredOrderByIOSSCodeSpecification(taxnumber.IOSSCode));
                var sort = new Dictionary<System.Linq.Expressions.Expression<Func<IOSSDeclaredOrder, dynamic>>, SortOrder>();
                sort.Add(p => p.CreateTime, SortOrder.Descending);
                var pageResult = await _iossdeclaredOrderRepository.FindInPageAsync(param.PageNumber, param.PageSize, specification, sort);

                result.PageNumber = pageResult.PageNumber;
                result.PageSize = pageResult.PageSize;
                result.TotalPages = pageResult.TotalPages;
                result.TotalRecords = pageResult.TotalRecords;
                result.Data = _typeAdapter.Adapt<List<TaxDeclaredModel>>(pageResult.Data);
            }
            return result;
        }
        /// <summary>
        /// 模糊查询注册主体
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<List<CompanyInfoModel>> GetCompanysByName(string name)
        {
            List<CompanyInfoModel> result = new List<CompanyInfoModel>();
            ISpecification<CompanyInfo> specification = new MatchCompanyInfoByIsDeletedSpecification(false);
            if (!name.IsNullOrBlank())
            {
                specification = specification.And(new MatchCompanyInfoContainNameSpecification(name));
            }
            var companys = await _companyInfoReadOnlyRepository.GetListAsync(specification);
            result = _typeAdapter.Adapt<List<CompanyInfoModel>>(companys);
            return result;
        }
        /// <summary>
        /// 根据公司名称模糊查询注册主体
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<List<CompanySimpleModel>> GetSimpleCompanysByName(string name, bool isContainTax)
        {
            List<CompanySimpleModel> result = new List<CompanySimpleModel>();
            ISpecification<CompanyInfo> specification = new MatchCompanyInfoByIsDeletedSpecification(false);
            specification = specification.And(new MatchCompanyInfoContainCnNameSpecification(name));
            var companys = await _companyInfoReadOnlyRepository.GetListAsync(specification);
            result = _typeAdapter.Adapt<List<CompanySimpleModel>>(companys);
            if (isContainTax)
            {
                foreach (var item in result)
                {
                    var taxs = await _taxNumberRepository.GetListAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                        .And(new MatchTaxNumerByIsAbleToDeclareSpecification(true))
                          //.And(new MatchTaxNumberByTaxTypeSpecification(TaxType.VAT))
                          .And(new MatchTaxNumberByRegisterMainSpecification(item.Id)));
                    item.CountryAndTaxNos = taxs.Select(t => new CountryAndTaxNo { CountryCode = t.TaxCountry.Code, TaxNo = t.TaxType == TaxType.VAT ? t.GetDeclaredObjectValue() : t.IOSSCode }).ToList();
                }
            }
            return result;
        }

        /// <summary>
        /// 根据税号模糊查询税号列表
        /// </summary>
        /// <param name="taxno"></param>
        /// <returns></returns>
        public async Task<List<CompanySimpleModel>> GetSimpleTaxList(string taxno)
        {
            var taxs = await _taxNumberRepository.GetListAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                       .And(new MatchTaxNumerByIsAbleToDeclareSpecification(true))
                         .And(new MatchTaxNumberByTaxNoSpecification(taxno)));
            var result = taxs.Select(t => new CompanySimpleModel { Id = t.Id, CompanyName = t.CompanyInfo.CnName, Email = t.Tenant.Name, TenantCode = t.Tenant.Code, TaxNo = t.GetDeclaredObjectValue() }).ToList();
            return result;
        }
        /// <summary>
        /// 查询购买服务记录
        /// </summary>
        /// <param name="taxid"></param>
        /// <returns></returns>
        public async Task<List<PaymentModel>> GetPaymentList(string taxid)
        {
            //var result = new PageResult<TaxNumberListModel>();
            var taxnumber = await _taxNumberRepository.GetByKeyAsync(taxid);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<Payment, dynamic>>, SortOrder>();
            sort.Add(p => p.CreateTime, SortOrder.Descending);

            var payments = await _paymentRepository.GetListAsync(new MatchPaymentByIsDeleteSpecification(false)
                .And(new MatchPaymentByTenantCodeSpecification(taxnumber.Tenant.Code))
                .And(new MatchPaymentByTaxNoSpecification(taxnumber.GetDeclaredObjectValue()))
                .And(new MatchPaymentByCountryCodeSpecification(taxnumber.TaxCountry.Code)), sort);

            var result = _typeAdapter.Adapt<List<PaymentModel>>(payments);

            return result;
        }

        /// <summary>
        /// 工作台统计
        /// </summary>
        /// <returns></returns>
        public async Task<DashboardStatisticModel> DashboardStatistic()
        {
            DashboardStatisticModel result = new DashboardStatisticModel();
            //税代配置
            var agents = await _taxAgentQueries.GetAllTaxAgentSupportBusinessInfo();
            List<AgentSimpleConfig> agentconfigs = new List<AgentSimpleConfig>();
            var userInfo = _adminIdentityService.GetAdminUserIdentity();
            var groupsId = userInfo.Groups.ToList();

            ISpecification<DeclaredOrder> declare_specification = new MatchDeclaredOrderByIsDeleteSpecification(false)
                .And(new MatchDeclaredOrderByIsTesterSpecification(false));

            ISpecification<RegisterOrder> register_specification = new MatchRegisterOrderByIsDeleteSpecification(false)
                 .And(new MatchRegisterOrderByIsTesterSpecification(false));

            ISpecification<RegisterOrder> agentConvert_specification = new MatchRegisterOrderByIsDeleteSpecification(false)
                 .And(new MatchRegisterOrderByIsTesterSpecification(false));

            ISpecification<TaxNumber> tax_specification = new MatchTaxNumerByIsDeletedSpecification(false)
                .And(new MatchTaxNumberByIsTesterSpecification(false));

            ISpecification<Domain.AggregatesModel.IOSSOrderAgg.IOSSRegisterOrder> ioss_specification = new MatchIOSSRegisterOrderByIsDeleteSpecification(false)
                .And(new MatchIOSSRegisterOrderByIsTesterSpecification(false));

            ISpecification<IOSSDeclaredOrder> iossdeclare_specification = new MatchIOSSDeclaredOrderByIsDeleteSpecification(false)
                .And(new MatchIOSSDeclaredOrderByIsTesterSpecification(false));

            ISpecification<Domain.AggregatesModel.OtherServiceOrderAgg.OtherServiceOrder> other_specification = new MatchOtherOrderByIsDeleteSpecification(false)
                .And(new MatchOtherOrderByIsTesterSpecification(false));
            //根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            if (groupsId.All(x => x != 1) && groupsId.All(x => x != 112) && groupsId.All(x => x != 113) && groupsId.All(x => x != 114))
            {
                declare_specification = declare_specification.And(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                register_specification = register_specification.And(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                agentConvert_specification = agentConvert_specification.And(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                tax_specification = tax_specification.And(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                ioss_specification = ioss_specification.And(new MatchIOSSRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchIOSSRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                other_specification = other_specification.And(new MatchOtherOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchOtherOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                iossdeclare_specification = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchIOSSDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));
                //超级销售经理
                //if (groupsId.Exists(x => x == 112))
                //{
                //    declare_specification = declare_specification.And(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperSaleManager));

                //    register_specification = register_specification.And(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperSaleManager));

                //    agentConvert_specification = agentConvert_specification.And(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperSaleManager));

                //    tax_specification = tax_specification.And(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.SuperSaleManager));

                //    ioss_specification = ioss_specification.And(new MatchIOSSRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperSaleManager));

                //    other_specification = other_specification.And(new MatchOtherOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperSaleManager));

                //    iossdeclare_specification = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperSaleManager));
                //}
                ////超级客服经理
                //if (groupsId.Exists(x => x == 113))
                //{
                //    declare_specification = declare_specification.And(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperCustomerManager));

                //    register_specification = register_specification.And(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperCustomerManager));

                //    agentConvert_specification = agentConvert_specification.And(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperCustomerManager));

                //    tax_specification = tax_specification.And(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.SuperCustomerManager));

                //    ioss_specification = ioss_specification.And(new MatchIOSSRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperCustomerManager));

                //    other_specification = other_specification.And(new MatchOtherOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperCustomerManager));

                //    iossdeclare_specification = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SuperCustomerManager));
                //}
                ////销售经理或者是客服经理
                //if (!groupsId.Exists(x => x == 112) && !groupsId.Exists(x => x == 113))
                //{
                //    declare_specification = declare_specification.And(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                //    register_specification = register_specification.And(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                //    agentConvert_specification = agentConvert_specification.And(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                //    tax_specification = tax_specification.And(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                //    ioss_specification = ioss_specification.And(new MatchIOSSRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchIOSSRegisterOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                //    other_specification = other_specification.And(new MatchOtherOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchOtherOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

                //    iossdeclare_specification = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchIOSSDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));
                //}

            }
            #region    申报订单
            //待申报
            var declare_specification_todeclare = declare_specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare))
                .And(new MatchDeclaredOrderByCreateTimeSpecification(new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1), DateTime.Now));
            long todeclareAmount = await _declaredOrderRepository.CountAsync(declare_specification_todeclare);


            //待审核
            var declare_specification_waitaudit = declare_specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.UnAudit)).And(new MatchDeclaredOrderByUnAuditOperationSpecification(OperationType.Audit));
            var waitauditAmount = await _declaredOrderRepository.CountAsync(declare_specification_waitaudit);

            //待提交税代
            var declare_specification_waitCommitTaxAgent = declare_specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.UnAudit)).And(new MatchDeclaredOrderByUnAuditOperationNotAnySpecification());
            var waitCommitTaxAgentAmount = await _declaredOrderRepository.CountAsync(declare_specification_waitCommitTaxAgent);

            //受理中
            var declare_specification_accepting = declare_specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Accepting));
            long acceptingAmount = await _declaredOrderRepository.CountAsync(declare_specification_accepting);
            //待下载缴税支付水单（1）WaitDownTaxPayment
            var declare_specification_todownloadPaymentFile = declare_specification.And(new MatchDeclaredOrderByToDownloadPaymentVoucherSpecification().And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Accepting)));
            long todownloadPaymentFileAmount = await _declaredOrderRepository.CountAsync(declare_specification_todownloadPaymentFile);
            //待上传缴税回执（2）WaitUploadTaxReceipt
            var declare_specification_touploadpaymentreceipt = declare_specification.And(new MatchDeclaredOrderByToUploadTaxReceiptSpecification().And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Finished)).And(new MatchDeclaredOrderByDeclaredTypeSpecification(DeclareBusinessCode.NonZeroDeclaration)));
            var touploadpaymentreceiptAmount = await _declaredOrderRepository.CountAsync(declare_specification_touploadpaymentreceipt);
            //规定时效未申报（3）NotDeclareInTime
            var unDeclaredOvertime = declare_specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare))
                .And(new MatchDeclaredOrderByToDeclaredOverTimeSpecification());
            long unDeclaredOvertimeAmount = await _declaredOrderRepository.CountAsync(unDeclaredOvertime);

            //规定时效内未回传支付水单（4）NotUploadPaymentInTime
            long unUploadPaymentOvertimeAmount = await _declaredOrderRepository.CountAsync(declare_specification.And(new MatchDeclaredOrderByToUploadPaymentSpecification()).And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Accepting)));
            //待续费
            //var declare_specification_unpaid =declare_specification_unaudit.And(new MatchDeclaredOrderByOperationTypeSpecification(OperationType.MarkPaid));
            // long unpaidAmount = await _declaredOrderRepository.CountAsync(declare_specification_unpaid);
            // //消费额度预警
            // var tax_specification_warning = tax_specification.And(new MatchTaxNumberByDeclaredResidueAmountSpecification(1));
            // long warningAmount_tax = await _taxNumberRepository.CountAsync(tax_specification_warning);

            result.DeclaredOrderStatistic = new DeclaredOrderStatistic
            {
                UnCommitTaxAgent = waitCommitTaxAgentAmount,
                ToDeclare = todeclareAmount,
                Accepting = acceptingAmount,
                UnAudit = waitauditAmount,
                ToDownloadPaymentFile = todownloadPaymentFileAmount,
                ToUploadPaymentReceipt = touploadpaymentreceiptAmount,
                UnDeclaredOvertime = unDeclaredOvertimeAmount,
                UnUploadPaymentOvertime = unUploadPaymentOvertimeAmount,
                //ToPaid= unpaidAmount,
                //ToWarning= warningAmount_tax
            };
            #endregion
            #region  注册订单
            //待审核
            var register_specification_waitaudit = register_specification.And(new MatchRegisterOrderByStatusSpecification(DeclaredOrderStatus.UnAudit)).And(new MatchRegisterOrderByUnAuditOperationSpecification(OperationType.Audit)).And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.Register));
            var waitauditAmount_register = await _registerOrderRepository.CountAsync(register_specification_waitaudit);

            //待提交税代
            var register_specification_waitCommitTaxAgent = register_specification.And(new MatchRegisterOrderByStatusSpecification(DeclaredOrderStatus.UnAudit)).And(new MatchRegisterOrderByUnAuditOperationNotAnySpecification()).And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.Register));
            var waitCommitTaxAgentAmount_register = await _registerOrderRepository.CountAsync(register_specification_waitCommitTaxAgent);



            //待上传受理回执
            var register_specification_touploadacceptreceipt = register_specification
                .And(new MatchRegisterOrderByAcceptedFileSpecification()).And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.Register));
            long touploadacceptreceiptAmount_register = await _registerOrderRepository.CountAsync(register_specification_touploadacceptreceipt);
            //受理中-待上传税号证书
            var register_specification_accepting = register_specification.And(new MatchRegisterOrderByUnUploadVatProofSpecification().And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.Register)));
            long acceptingAmount_register = await _registerOrderRepository.CountAsync(register_specification_accepting);
            //已完成-待上传其他回传文件
            var register_specification_touploadTaxproof = register_specification.And(new MatchRegisterOrderByUnUploadOtherFileSpecification().And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.Register)));
            var needuploadfile = await _registerOrderRepository.GetListAsync(register_specification_touploadTaxproof);
            long touploadTaxproofAmount_register = 0;
            foreach (var item in needuploadfile)
            {
                try
                {
                    var namelist = item.AcceptUploadFileConfig.Where(f => f.IsMustUpload).Select(f => f.FileName).ToList();
                    var namelist2 = item.TaxNumber.RegisterFiles.AcceptUploadFiles.Select(f => f.Name).ToList();
                    if (namelist.Except(namelist2).Any())
                    {
                        touploadTaxproofAmount_register++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"统计工作台数据时出现异常 ：{ex.Message}");
                }
            }
            //long touploadTaxproofAmount_register = await _registerOrderRepository.CountAsync(register_specification_touploadTaxproof);
            //已完成-待上传FRS确认函
            var register_specification_touploadfrsfile = register_specification.And(new MatchRegisterOrderByToUploadFRSSpecification().And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.Register)));
            long touploadfrsfileAmount_register = await _registerOrderRepository.CountAsync(register_specification_touploadfrsfile);
            //已完成-待上传EORI
            var register_specification_touploadEORI = register_specification.And(new MatchRegisterOrderByToUploadEORISpecification().And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.Register)));
            long touploadEORIAmount_register = await _registerOrderRepository.CountAsync(register_specification_touploadEORI);
            //待续费
            //var register_specification_unpaid = register_specification_unaudit.And(new MatchRegisterOrderByOperationTypeSpecification(OperationType.MarkPaid));
            //long unpaidAmount_register = await _registerOrderRepository.CountAsync(register_specification_unpaid);

            result.RegisterOrderStatistic = new RegisterOrderStatistic
            {
                UnAudit = waitauditAmount_register,
                UnCommitTaxAgent = waitCommitTaxAgentAmount_register,
                ToUploadAcceptReceipt = touploadacceptreceiptAmount_register,
                ToUploadEORI = touploadEORIAmount_register,
                ToUploadFrsFile = touploadfrsfileAmount_register,
                ToUploadTaxProof = touploadTaxproofAmount_register,
                ToUploadVatProof = acceptingAmount_register,
                //ToPaid=unpaidAmount_register,
            };
            #endregion
            #region 转代理订单
            //待审核
            var agentConvert_specification_waitaudit = agentConvert_specification.And(new MatchRegisterOrderByStatusSpecification(DeclaredOrderStatus.UnAudit)).And(new MatchRegisterOrderByUnAuditOperationSpecification(OperationType.Audit)).And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.AgentConvert));
            var waitauditAmount_agentConvert = await _registerOrderRepository.CountAsync(agentConvert_specification_waitaudit);

            //待提交税代
            var agentConvert_specification_waitCommitTaxAgent = agentConvert_specification.And(new MatchRegisterOrderByStatusSpecification(DeclaredOrderStatus.UnAudit)).And(new MatchRegisterOrderByUnAuditOperationNotAnySpecification()).And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.AgentConvert));
            var waitCommitTaxAgentAmount_agentConvert = await _registerOrderRepository.CountAsync(agentConvert_specification_waitCommitTaxAgent);

            //受理中
            var agentConvert_specification_accepting = agentConvert_specification.And(new MatchRegisterOrderByStatusSpecification(DeclaredOrderStatus.Accepting).And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.AgentConvert)));
            long acceptingAmount_agentConvert = await _registerOrderRepository.CountAsync(agentConvert_specification_accepting);

            result.AgentConvertOrderStatistic = new AgentConvertOrderStatistic
            {
                UnAudit = waitauditAmount_agentConvert,
                UnCommitTaxAgent = waitCommitTaxAgentAmount_agentConvert,
                Accepting = acceptingAmount_agentConvert
            };
            #endregion
            #region  税号
            //待受理
            var tax_specification_unaudit = register_specification.And(new MatchRegisterOrderByStatusSpecification(TaxNumberStatus.UnAuthorize).And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.AgentConvert)));
            long unauditAmount_tax = await _registerOrderRepository.CountAsync(tax_specification_unaudit);
            //受理中
            var tax_specification_accepting = register_specification.And(new MatchRegisterOrderByStatusSpecification(TaxNumberStatus.Authorizing).And(new MatchRegisterOrderByBusinessTypeSpecification(RegisterBusinessCode.AgentConvert)));
            long acceptingAmount_tax = await _registerOrderRepository.CountAsync(tax_specification_accepting);
            //待续费
            //var tax_specification_unpaid = tax_specification_unaudit.And(new MatchRegisterOrderByOperationTypeSpecification(OperationType.MarkPaid));
            //long unpaidAmount_tax = await _registerOrderRepository.CountAsync(tax_specification_unpaid);

            result.TaxNumberStatistic = new TaxNumberStatistic
            {
                Accepting = acceptingAmount_tax,
                UnAudit = unauditAmount_tax,
                //ToPaid = unpaidAmount_tax
            };
            #endregion
            #region  其他服务订单

            //待受理
            var other_specification_unaudit = other_specification.And(new MatchOtherOrderByStatusSpecification(OtherOrderStatus.UnAudit));
            long unauditAmount_other = await _otherServiceOrderRepository.CountAsync(other_specification_unaudit);
            //受理中
            var other_specification_accepting = other_specification.And(new MatchOtherOrderByStatusSpecification(OtherOrderStatus.Accepting));
            long acceptingAmount_other = await _otherServiceOrderRepository.CountAsync(other_specification_accepting);
            result.OtherOrderStatistic = new OtherOrderStatistic
            {
                Accepting = acceptingAmount_other,
                UnAudit = unauditAmount_other
            };
            #endregion
            #region IOSS订单
            //待受理
            var ioss_specification_unaudit = ioss_specification.And(new MatchIOSSRegisterOrderByStatusSpecification(OtherOrderStatus.UnAudit));
            long unauditAmount_ioss = await _iossRegisterOrderRepository.CountAsync(ioss_specification_unaudit);

            //待审核
            var ioss_specification_waitaudit = ioss_specification.And(new MatchIOSSRegisterOrderByStatusSpecification(OtherOrderStatus.UnAudit)).And(new MatchIOSSRegisterOrderByUnAuditOperationSpecification(OperationType.Audit));
            long waitauditAmount_ioss = await _iossRegisterOrderRepository.CountAsync(ioss_specification_waitaudit);

            //待提交税代
            var ioss_specification_waitCommitTaxAgent = ioss_specification.And(new MatchIOSSRegisterOrderByStatusSpecification(OtherOrderStatus.UnAudit)).And(new MatchIOSSRegisterOrderByUnAuditOperationNotAnySpecification());
            long waitCommitTaxAgentAmount_ioss = await _iossRegisterOrderRepository.CountAsync(ioss_specification_waitCommitTaxAgent);

            //受理中
            var ioss_specification_accepting = ioss_specification.And(new MatchIOSSRegisterOrderByStatusSpecification(OtherOrderStatus.Accepting));
            long acceptingAmount_ioss = await _iossRegisterOrderRepository.CountAsync(ioss_specification_accepting);
            result.IOSSRegisterOrderStatistic = new IOSSRegisterOrderStatistic
            {
                Accepting = acceptingAmount_ioss,
                UnAudit = waitauditAmount_ioss,
                UnCommitTaxAgent = waitCommitTaxAgentAmount_ioss
            };
            #endregion
            #region    IOSS申报订单
            //待申报
            var iossdeclare_specification_todeclare = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare))
                .And(new MatchIOSSDeclaredOrderByCreateTimeSpecification(new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1), DateTime.Now));
            long iosstodeclareAmount = await _iossdeclaredOrderRepository.CountAsync(iossdeclare_specification_todeclare);
            //待受理
            //var iossdeclare_specification_unaudit = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByStatusSpecification(DeclaredOrderStatus.UnAudit));
            //long iossunauditAmount = await _iossdeclaredOrderRepository.CountAsync(iossdeclare_specification_unaudit);


            //待审核
            var iossdeclare_specification_waitaudit = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByStatusSpecification(DeclaredOrderStatus.UnAudit)).And(new MatchIOSSDeclaredOrderByUnAuditOperationSpecification(OperationType.Audit));
            var waitauditAmount_iossdeclare = await _iossdeclaredOrderRepository.CountAsync(iossdeclare_specification_waitaudit);

            //待提交税代
            var iossdeclare_specification_waitCommitTaxAgent = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByStatusSpecification(DeclaredOrderStatus.UnAudit)).And(new MatchIOSSDeclaredOrderByUnAuditOperationNotAnySpecification());
            var waitCommitTaxAgentAmount_iossdeclare = await _iossdeclaredOrderRepository.CountAsync(iossdeclare_specification_waitCommitTaxAgent);


            //受理中
            var iossdeclare_specification_accepting = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Accepting));
            long iossacceptingAmount = await _iossdeclaredOrderRepository.CountAsync(iossdeclare_specification_accepting);
            //待下载缴税支付水单（1）WaitDownTaxPayment
            var iossdeclare_specification_todownloadPaymentFile = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByToDownloadPaymentVoucherSpecification()
                .And(new MatchIOSSDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Accepting)));
            long iosstodownloadPaymentFileAmount = await _iossdeclaredOrderRepository.CountAsync(iossdeclare_specification_todownloadPaymentFile);
            //待上传缴税回执（2）WaitUploadTaxReceipt
            var iossdeclare_specification_touploadpaymentreceipt = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByToUploadTaxReceiptSpecification()
                .And(new MatchIOSSDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Finished))
                .And(new MatchIOSSDeclaredOrderByDeclaredTypeSpecification(DeclareBusinessCode.NonZeroDeclaration)));
            var iosstouploadpaymentreceiptAmount = await _iossdeclaredOrderRepository.CountAsync(iossdeclare_specification_touploadpaymentreceipt);
            //规定时效未申报（3）NotDeclareInTime
            var iossunDeclaredOvertime = iossdeclare_specification.And(new MatchIOSSDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare))
                .And(new MatchIOSSDeclaredOrderByToDeclaredOverTimeSpecification());
            long iossunDeclaredOvertimeAmount = await _iossdeclaredOrderRepository.CountAsync(iossunDeclaredOvertime);

            //规定时效内未回传支付水单（4）NotUploadPaymentInTime
            long iossunUploadPaymentOvertimeAmount = await _iossdeclaredOrderRepository.CountAsync(iossdeclare_specification.And(new MatchIOSSDeclaredOrderByToUploadPaymentSpecification())
                .And(new MatchIOSSDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Accepting)));

            result.IOSSDeclaredOrderStatistic = new DeclaredOrderStatistic
            {
                ToDeclare = iosstodeclareAmount,
                Accepting = iossacceptingAmount,
                UnAudit = waitauditAmount_iossdeclare,
                UnCommitTaxAgent = waitCommitTaxAgentAmount_iossdeclare,
                ToDownloadPaymentFile = iosstodownloadPaymentFileAmount,
                ToUploadPaymentReceipt = iosstouploadpaymentreceiptAmount,
                UnDeclaredOvertime = iossunDeclaredOvertimeAmount,
                UnUploadPaymentOvertime = iossunUploadPaymentOvertimeAmount,
                //ToPaid= unpaidAmount,
                //ToWarning= warningAmount_tax
            };
            #endregion
            return result;
        }

        #region 税号主体
        public async Task<List<CompanyNameModel>> GetCompanyNames(CompanyQueryParam param)
        {
            List<CompanyNameModel> result = new List<CompanyNameModel>();
            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<Vat.Service.Domain.AggregatesModel.TaxAgg.CompanyInfo, dynamic>>, SortOrder>();
            sort.Add(p => p.CnName, SortOrder.Ascending);

            var companys = await _companyInfoReadOnlyRepository.GetListAsync(new MatchCompanyInfoByIsDeletedSpecification(false)
               .And(new MatchCompanyInfoByTenantSpecification(param.TenantCode)), sort);

            var taxnumbers = await _taxNumberRepository.GetListAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                .And(new MatchTaxNumberByTenantCodeSpecification(param.TenantCode))
                .And(new MatchTaxNumberByVatCountrySpecification(param.CountryCode)));
            //转代理需要包含待授权，已解除状态税号
            if (param.BusinessType == RegisterBusinessCode.AgentConvert)
            {
                List<string> statuslist = new List<string> { TaxNumberStatus.UnAuthorize, TaxNumberStatus.Revoke };
                taxnumbers = taxnumbers.Where(t => !statuslist.Contains(t.Status));
            }
            List<string> exceptCompIds = taxnumbers.Select(t => t.CompanyInfo.Id).ToList();

            companys = companys.Where(c => !exceptCompIds.Contains(c.Id)).ToList();
            result = _typeAdapter.Adapt<List<CompanyNameModel>>(companys);
            return result;
        }
        #endregion

        /// <summary>
        /// 查询税号未完成的待申报
        /// </summary>
        /// <param name="taxid"></param>
        /// <returns></returns>
        public async Task<List<UnFinishedDeclare>> GetUnFinishedDeclared(string taxid)
        {
            var taxnumber = await _taxNumberRepository.GetByKeyAsync(taxid);
            List<UnFinishedDeclare> result = new List<UnFinishedDeclare>();
            List<string> status = new List<string> { DeclaredOrderStatus.Finished, DeclaredOrderStatus.Canceled };
            if (taxnumber.TaxType == TaxType.VAT)
            {
                var orderlist = await _declaredOrderReadOnlyRepository.GetListAsync(new MatchDeclaredOrderByIsDeleteSpecification(false)
                    .And(new MatchDeclaredOrderByTaxNoSpecification(taxnumber.GetDeclaredObjectValue())));
                result = orderlist.Where(o => !status.Contains(o.Status))
                    .Select(o => new UnFinishedDeclare() { Status = o.Status, DeclarationCycle = o.DeclarationCycle.ToDTO() }).ToList();
            }
            else
            {
                var orderlist = await _iossdeclaredOrderReadOnlyRepository.GetListAsync(new MatchIOSSDeclaredOrderByIsDeleteSpecification(false)
                       .And(new MatchIOSSDeclaredOrderByIOSSCodeSpecification(taxnumber.IOSSCode)));
                result = orderlist.Where(o => !status.Contains(o.Status))
                    .Select(o => new UnFinishedDeclare() { Status = o.Status, DeclarationCycle = o.DeclarationCycle.ToDTO() }).ToList();
            }
            return result;
        }
        /// <summary>
        /// 现在vat注册文件包
        /// </summary>
        /// <param name="oid"></param>
        /// <returns></returns>
        public async Task<Stream> GetTaxNumberDataPackageByOrderId(List<string> oid)
        {
            //查询税号id
            var orders = await _registerOrderRepository.GetListAsync(new MatchRegisterOrderByOrderIdsSpecification(oid));
            if (!orders.Any())
                throw new VatCommonException(Error.Codes.NoFoundRegisterOrder, Error.Names.NoFoundRegisterOrder);
            List<string> taxids = orders.Select(o => o.TaxNumber.Id).ToList();
            ISpecification<TaxNumber> specification = new MatchTaxNumerByIsDeletedSpecification(false);
            var userInfo = _adminIdentityService.GetAdminUserIdentity();
            var groupsId = userInfo.Groups.ToList();
            ////根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            if (groupsId.All(x => x != 1) && groupsId.All(x => x != 112) && groupsId.All(x => x != 113) && groupsId.All(x => x != 114))
            {
                specification = specification.And(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager).Or(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.SaleManager)));
            }
            specification = specification.And(new MatchTaxNumerByOrderIdsSpecification(taxids));
            var query = await _taxNumberRepository.GetListAsync(specification);

            var paraList = new List<string>();
            Dictionary<string, List<string>> fileNameDic = new Dictionary<string, List<string>>();
            var euCountryDic = ConstantHelper.GetConstantInfoList(typeof(EUCountryInfo)).ToDictionary(c => c.Value, c => c.Description);
            euCountryDic.Add("GB", "英国");
            CustomerFile cf = new CustomerFile()
            {
                RequestTenantCode = "Admin"
            };
            Dictionary<string, string> signedDic = new Dictionary<string, string>();

            foreach (var vatRegister in query)
            {
                var fileNameList = new List<string>();
                if (vatRegister.BusinessType.Equals(Domain.Constants.Type.RegisterBusinessCode.AgentConvert))
                {
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.VatProof))
                    {
                        paraList.Add(vatRegister.RegisterFiles.VatProof);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.VatProof));
                    }
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.OldVatProof))
                    {
                        paraList.Add(vatRegister.RegisterFiles.OldVatProof);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.OldVatProof));
                    }
                    if (!string.IsNullOrEmpty(vatRegister.FrsEntity.FrsFile))
                    {
                        paraList.Add(vatRegister.FrsEntity.FrsFile);
                        fileNameList.Add(Path.GetFileName(vatRegister.FrsEntity.FrsFile));
                    }
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.AgencyAuthorization))
                    {
                        paraList.Add(vatRegister.RegisterFiles.AgencyAuthorization);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.AgencyAuthorization));
                    }
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.AcceptedFile))
                    {
                        paraList.Add(vatRegister.RegisterFiles.AcceptedFile);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.AcceptedFile));
                    }
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.OtherFile))
                    {
                        paraList.Add(vatRegister.RegisterFiles.OtherFile);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.OtherFile));
                    }
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.TaxProof))
                    {
                        paraList.Add(vatRegister.RegisterFiles.TaxProof);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.TaxProof));
                    }
                    if (vatRegister.RegisterFiles?.BackupFiles != null && vatRegister.RegisterFiles.BackupFiles.Any())
                    {
                        paraList.AddRange(vatRegister.RegisterFiles.BackupFiles);
                        fileNameList.AddRange(vatRegister.RegisterFiles.BackupFiles.Select(c => Path.GetFileName(c)).ToList());
                    }
                    if (vatRegister.RegisterFiles?.AcceptUploadFiles != null && vatRegister.RegisterFiles.AcceptUploadFiles.Any())
                    {
                        paraList.AddRange(vatRegister.RegisterFiles.AcceptUploadFiles.Select(c => c.Path));
                        fileNameList.AddRange(vatRegister.RegisterFiles.AcceptUploadFiles.Select(c => Path.GetFileName(c.Path)).ToList());
                    }
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles?.ApplyFormBySystem))
                    {
                        paraList.Add(vatRegister.RegisterFiles.ApplyFormBySystem);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.ApplyFormBySystem));
                    }
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles?.SignedApplyForm))
                    {
                        paraList.Add(vatRegister.RegisterFiles.SignedApplyForm);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.SignedApplyForm));
                    }
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles?.ApplyForm))
                    {
                        paraList.Add(vatRegister.RegisterFiles.ApplyForm);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.ApplyForm));
                    }
                    if (!string.IsNullOrEmpty(vatRegister.RegisterFiles?.AuthorizationFile))
                    {
                        paraList.Add(vatRegister.RegisterFiles.AuthorizationFile);
                        fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.AuthorizationFile));
                    }
                    if (vatRegister.RegisterFiles?.OtherModuleFiles != null && vatRegister.RegisterFiles.OtherModuleFiles.Any())
                    {
                        paraList.AddRange(vatRegister.RegisterFiles.OtherModuleFiles.Select(f => f.Path));
                        fileNameList.AddRange(vatRegister.RegisterFiles.OtherModuleFiles.Select(f => Path.GetFileName(f.Path)).ToList());
                    }
                }
                else
                {
                    if (vatRegister.RegisterFiles != null)
                    {
                        if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.TaxProof))
                        {
                            paraList.Add(vatRegister.RegisterFiles.TaxProof);
                            fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.TaxProof));
                        }
                        if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.VatProof))
                        {
                            paraList.Add(vatRegister.RegisterFiles.VatProof);
                            fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.VatProof));
                        }
                        if (vatRegister.RegisterFiles?.BackupFiles != null && vatRegister.RegisterFiles.BackupFiles.Any())
                        {
                            paraList.AddRange(vatRegister.RegisterFiles.BackupFiles);
                            fileNameList.AddRange(vatRegister.RegisterFiles.BackupFiles.Select(c => Path.GetFileName(c)).ToList());
                        }
                        if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.AgencyAuthorization))
                        {
                            paraList.Add(vatRegister.RegisterFiles.AgencyAuthorization);
                            fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.AgencyAuthorization));
                        }
                        if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.AcceptedFile))
                        {
                            paraList.Add(vatRegister.RegisterFiles.AcceptedFile);
                            fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.AcceptedFile));
                        }
                        if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.OtherFile))
                        {
                            paraList.Add(vatRegister.RegisterFiles.OtherFile);
                            fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.OtherFile));
                        }
                        if (vatRegister.RegisterFiles?.AcceptUploadFiles != null && vatRegister.RegisterFiles.AcceptUploadFiles.Any())
                        {
                            paraList.AddRange(vatRegister.RegisterFiles.AcceptUploadFiles.Select(c => c.Path));
                            fileNameList.AddRange(vatRegister.RegisterFiles.AcceptUploadFiles.Select(c => Path.GetFileName(c.Path)).ToList());
                        }
                        if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.ApplyFormBySystem))
                        {
                            paraList.Add(vatRegister.RegisterFiles.ApplyFormBySystem);
                            fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.ApplyFormBySystem));
                        }
                        if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.SignedApplyForm))
                        {
                            paraList.Add(vatRegister.RegisterFiles.SignedApplyForm);
                            fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.SignedApplyForm));
                        }
                        if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.ApplyForm))
                        {
                            paraList.Add(vatRegister.RegisterFiles.ApplyForm);
                            fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.ApplyForm));
                        }
                        if (!string.IsNullOrEmpty(vatRegister.RegisterFiles.AuthorizationFile))
                        {
                            paraList.Add(vatRegister.RegisterFiles.AuthorizationFile);
                            fileNameList.Add(Path.GetFileName(vatRegister.RegisterFiles.AuthorizationFile));
                        }
                        if (vatRegister.RegisterFiles.OtherModuleFiles != null && vatRegister.RegisterFiles.OtherModuleFiles.Any())
                        {
                            paraList.AddRange(vatRegister.RegisterFiles.OtherModuleFiles.Select(f => f.Path));
                            fileNameList.AddRange(vatRegister.RegisterFiles.OtherModuleFiles.Select(f => Path.GetFileName(f.Path)).ToList());
                        }
                    }

                }
                if (vatRegister.CompanyInfo?.EORIEntities != null)
                {
                    foreach (var item in vatRegister.CompanyInfo.EORIEntities)
                    {
                        if (item.File.IsNullOrBlank() || item.CountryCode != vatRegister.TaxCountry.Code)
                            continue;
                        paraList.Add(item.File);
                        fileNameList.Add(Path.GetFileName(item.File));
                    }
                }
                //if (vatRegister.CompanyInfo != null && vatRegister.CompanyInfo.EnterpriseFile != null)
                //{
                //    if (!string.IsNullOrEmpty(vatRegister.CompanyInfo.EnterpriseFile.BusinessLicense))
                //    {
                //        paraList.Add(vatRegister.CompanyInfo.EnterpriseFile.BusinessLicense);
                //        fileNameList.Add(Path.GetFileName(vatRegister.CompanyInfo.EnterpriseFile.BusinessLicense));
                //    }

                //    if (!string.IsNullOrEmpty(vatRegister.CompanyInfo.EnterpriseFile.IDcardObverse))
                //    {
                //        paraList.Add(vatRegister.CompanyInfo.EnterpriseFile.IDcardObverse);
                //        fileNameList.Add(Path.GetFileName(vatRegister.CompanyInfo.EnterpriseFile.IDcardObverse));
                //    }
                //    if (!string.IsNullOrEmpty(vatRegister.CompanyInfo.EnterpriseFile.IDcardReverse))
                //    {
                //        paraList.Add(vatRegister.CompanyInfo.EnterpriseFile.IDcardReverse);
                //        fileNameList.Add(Path.GetFileName(vatRegister.CompanyInfo.EnterpriseFile.IDcardReverse));
                //    }
                //    if (!string.IsNullOrEmpty(vatRegister.CompanyInfo.EnterpriseFile.IDcardScan))
                //    {
                //        paraList.Add(vatRegister.CompanyInfo.EnterpriseFile.IDcardScan);
                //        fileNameList.Add(Path.GetFileName(vatRegister.CompanyInfo.EnterpriseFile.IDcardScan));
                //    }
                //}
                //下载表单中的文件
                var order = orders.Where(o => o.TaxNumber.Id == vatRegister.Id).FirstOrDefault();
                if (order != null)
                {
                    var components = order.FormComponents.Where(c => c.Type.ToLower() == "image").Select(c => c.Id).ToList();
                    var filelist = order.FormData?.Where(d => components.Contains(d.FieldId) && !d.Value.IsNullOrBlank()).Select(d => d.Value).ToList();
                    foreach (var item in filelist)
                    {
                        if (paraList.Contains(item))
                            continue;
                        paraList.Add(item);
                        fileNameList.Add(Path.GetFileName(item));
                    }

                }
                //var vatno= vatRegister.GetDeclaredObjectValue();
                // vatno = !vatno.IsNullOrBlank() ? vatno.Replace("/", "") : vatno;
                //      var tax = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                //  .And(new MatchTaxNumberByTenantCodeSpecification(order.Tenant.Code))
                //.And(new MatchTaxNumberByNoSpecification(order.TaxNumber.TaxNumberEntity.VatNo)));
                var tax = await _taxNumberRepository.GetByKeyAsync(order.TaxNumber.Id);
                var managerCode = tax == null ? "" : tax.ManagerCode;
                string key = $"{managerCode}_{order.TaxNumber.CompanyInfo.EnName}_{order.TaxNumber.CompanyInfo.CnName}";
                string newkey = FileHelper.GetListIncreaseName(fileNameDic.Keys.ToList(), key);
                {
                    var signedid = order.FormComponents.Where(c => c.Type.ToLower() == "signed")?.Select(c => c.Id).FirstOrDefault();
                    var signedvalue = order.FormData.FirstOrDefault(d => d.FieldId == signedid)?.Value;
                    if (!signedvalue.IsNullOrBlank())
                    {
                        signedDic.Add(newkey, signedvalue);
                    }
                }
                fileNameDic.Add(newkey, fileNameList);
            }
            if (!paraList.Any())
            {
                //return new MemoryStream();
                throw new VatCommonException(Error.Codes.NotFiles, Error.Names.NotFiles);
            }
            foreach (var variable in paraList)
            {
                cf.RequestFilePath.Add(variable?.Substring(variable.LastIndexOf('/') + 1));
            }
            var para = Newtonsoft.Json.JsonConvert.SerializeObject(cf);
            var ms = _fileServiceMethod.GetFileServerMemoryStream(para, fileNameDic, signedDic);

            return ms;
        }
        #region 续费管理
        public async Task<PageResult<TaxRenewListModel>> GetRenewInPage(TaxRenewParam request)
        {
            var result = new PageResult<TaxRenewListModel>();
            result.PageNumber = request.PageNumber;
            result.PageSize = request.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            ISpecification<TaxNumber> specification = GetRenewSpecification(request);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<TaxNumber, dynamic>>, SortOrder>();
            sort.Add(p => p.CreateTime, SortOrder.Descending);

            var pageResult = await _taxNumberRepository.FindInPageAsync(request.PageNumber, request.PageSize, specification, sort);

            result.PageNumber = pageResult.PageNumber;
            result.PageSize = pageResult.PageSize;
            result.TotalPages = pageResult.TotalPages;
            result.TotalRecords = pageResult.TotalRecords;

            var dataList = new List<TaxRenewListModel>();
            if (pageResult.Data.Count > 0)
            {
                var productlist = await _productRepository.GetListAsync(new MatchProductByIsDeleteSpecification(false)
                      .And(new MatchProductByStatusSpecification("Saleing")));
                var dataTmp = pageResult.Data;
                dataList = _typeAdapter.Adapt<List<TaxRenewListModel>>(dataTmp);
                foreach (var item in dataList)
                {
                    //var priceInfo = new Dictionary<string, Vat.Service.Domain.Common.Models.Money>();
                    var type = "VatDeclare";
                    if (item.TaxCountry.Code == "IOSS")
                    {
                        type = "IOSSDeclare";
                    }
                    var price = productlist.Where(p => p.IsDelete == false && p.ProductContentConfig.MonthNumber == 12 && p.ProductContentConfig.CountryCode == item.TaxCountry.Code && p.ProductContentConfig.Type == type).FirstOrDefault()?.ProductPrice;
                    item.Price = price;
                }
            }
            result.Data = dataList;
            return result;
        }

        private ISpecification<TaxNumber> GetRenewSpecification(TaxRenewParam request)
        {
            var userInfo = _adminIdentityService.GetAdminUserIdentity();

            ISpecification<TaxNumber> specification = new MatchTaxNumerByIsDeletedSpecification(false);
            specification = specification.And(new MatchTaxNumberByStatusSpecification(TaxNumberStatus.Authorized));
            var groupsId = userInfo.Groups.ToList();
            ////根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            if (groupsId.All(x => x != 1) && groupsId.All(x => x != 112) && groupsId.All(x => x != 113) && groupsId.All(x => x != 114))
            {
                specification = specification.And(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchTaxNumberByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));
            }
            if (!request.KeyWords.IsNullOrBlank())
            {//关键字
                specification = specification.And(new MatchTaxNumberByRenewKeyWordSpecification(request.KeyWords.Trim()));
            }
            if (request.SaleManager != "all" && !request.SaleManager.IsNullOrBlank())
            {
                specification = specification.And(new MatchTaxNumberByManagerSpecification(request.SaleManager, ManagerType.SaleManager));
            }
            if (request.CustomerManager != "all" && !request.CustomerManager.IsNullOrBlank())
            {
                specification = specification.And(new MatchTaxNumberByManagerSpecification(request.CustomerManager, ManagerType.CustomerManager));
            }
            if (request.ServiceBeginDate != null || request.ServiceEndDate != null)
            {
                specification = specification.And(new MatchTaxNumberByDeclaredDeadlineRangeSpecification(request.ServiceBeginDate, request.ServiceEndDate));
            }
            if (request.IsTester != null)
            {
                specification = specification.And(new MatchTaxNumberByIsTesterSpecification(request.IsTester.Value));
            }
            if (!request.VatCountryCode.IsNullOrBlank())
            {
                specification = specification.And(new MatchTaxNumberByVatCountrySpecification(request.VatCountryCode));
            }
            if (!request.TaxServiceDeadlineType.IsNullOrBlank())
            {//剩余服务时长
                specification = specification.And(new MatchTaxNumerByServiceDateSpecification(request.TaxServiceDeadlineType));
            }
            return specification;
        }
        #endregion
        public async Task<Stream> ExportTaxNumbers(TaxNumberParam request)
        {
            var userInfo = _adminIdentityService.GetAdminUserIdentity();
            var groupsId = userInfo.Groups;
            var isSuperManager = false;
            ////根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            if (groupsId.Any(x => x == 1))
            {
                isSuperManager = true;
            }
            ISpecification<TaxNumber> specification = GetSpecification(request, true);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<TaxNumber, dynamic>>, SortOrder>();
            sort.Add(p => p.CreateTime, SortOrder.Descending);

            var taxNumbers = await _taxNumberRepository.GetListAsync(specification, sort);

            var stream = new MemoryStream();
            var path = Directory.GetCurrentDirectory();
            var template = Path.Combine(path, "StaticFiles", "vattaxnumber.xlsx");
            var excel = new NPOIExcel();
            excel.Open(template);
            excel.ActiveSheet(0);
            excel.SetCurrentSheet(0);
            //if (isSuperManager)
            //{
            //    excel.SetCurrentRow(0);
            //    excel.CreateCell(10);
            //    excel.WriteCell("税务代理");
            //}
            if (!taxNumbers.Any())
            {
                excel.Save(stream);
                return stream;
            }
            for (int i = 0; i < taxNumbers.Count(); i++)
            {
                WriteVatRegister(excel, i + 1, taxNumbers.ElementAt(i), isSuperManager);
            }
            excel.Save(stream);
            return stream;
        }

        void WriteVatRegister(NPOIExcel excel, int rowIndex,
         TaxNumber taxNumber, bool isSuperManager)
        {
            excel.CreateRow(rowIndex);

            //注册邮箱
            excel.CreateCell(0);
            //excel.WriteCell(taxNumber.Tenant?.Name);
            excel.WriteCell(taxNumber.Tenant.Name.IsNullOrBlank() ? "-" : taxNumber.Tenant.Name);
            excel.CreateCell(1);
            excel.WriteCell(taxNumber.Tenant.RegisterPhoneNo.IsNullOrBlank() ? "-" : taxNumber.Tenant.RegisterPhoneNo);
            excel.CreateCell(2);
            excel.WriteCell(taxNumber.Tenant?.Code);

            //税号主体（中文全称）
            excel.CreateCell(3);
            excel.WriteCell(taxNumber.CompanyInfo.CnName);

            //注册名称（英文）
            excel.CreateCell(4);
            excel.WriteCell(taxNumber.CompanyInfo.EnName);

            //EORI
            excel.CreateCell(5);
            excel.WriteCell(taxNumber.EORICode);

            //VAT No./企业税号
            excel.CreateCell(6);
            string taxno = $"VAT No.：{taxNumber.TaxNumberEntity.VatNo} ";
            if (taxNumber.TaxCountry.Code == "DE")
            {
                taxno = string.Concat(taxno, " \n", $"本地税号：{taxNumber.TaxNumberEntity.RefNo}");
            }
            else if (taxNumber.TaxCountry.Code == "FR")
            {
                taxno = string.Concat(taxno, " \n", $"SIRET号：{taxNumber.TaxNumberEntity.SIRET}");
            }
            excel.WriteCell(taxno);

            //国家
            excel.CreateCell(7);
            excel.WriteCell(taxNumber.TaxCountry.CnName);

            //申报税率
            excel.CreateCell(8);
            excel.WriteCell($"{taxNumber.TaxRate}%");

            //申报方式
            excel.CreateCell(9);
            excel.WriteCell(AttributeUtil.GetDescription<DeclareMethodType>(taxNumber.DeclareMethod));

            //销售经理
            excel.CreateCell(10);
            excel.WriteCell(taxNumber.SaleClientManager?.Name);

            //客服经理
            excel.CreateCell(11);
            excel.WriteCell(taxNumber.CustomerServiceManager?.Name);
            //税代
            excel.CreateCell(12);
            excel.WriteCell(isSuperManager ? taxNumber.TaxAgentEntity?.Name : "");

            //状态
            excel.CreateCell(13);
            excel.WriteCell(AttributeUtil.GetDescription<TaxNumberStatus>(taxNumber.Status));
            excel.CreateCell(14);
            excel.WriteCell(taxNumber.AgentManagerId);

        }
        /// <summary>
        /// 导出税代模板
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportTaxAgentTemplate(ExportTaxAgentTemplateParam request)
        {
            List<string> cellName = new List<string>();

            var taxagents = await _taxAgentQueries.GetTaxAgentForm();
            if (!request.TaxAgentCode.IsNullOrBlank())
            {
                taxagents = taxagents.Where(a => a.TaxAgentId == request.TaxAgentCode).ToList();
                //if (agent == null)
                //    throw new Exception("未找到税代模板");

                //cellName = agent.FormComponentModels.Where(c => c.Type != "district-link").Select(c => c.Label).ToList();
            }
            if (!request.CountryCode.IsNullOrBlank())
            {
                taxagents = taxagents.Where(a => a.CountryCode == request.CountryCode).ToList();
            }
            if (!request.BusinessType.IsNullOrBlank())
            {
                taxagents = taxagents.Where(a => a.BusinessType == request.BusinessType).ToList();
            }
            var stream = new MemoryStream();
            var path = Directory.GetCurrentDirectory();
            var template = Path.Combine(path, "StaticFiles", "taxagent.xlsx");
            var excel = new NPOIExcel();

            excel.Open(template);
            for (int i = 0; i < taxagents.Count; i++)
            {
                var agent = taxagents[i];
                cellName = agent.FormComponentModels.Where(c => c.Type != "district-link").Select(c => c.Label).ToList();

                excel.CreateSheet($"{agent.TaxAgentName}_{agent.CountryCode}_{agent.BusinessType}");
                excel.ActiveSheet(i + 1);
                excel.CreateRow(0);

                excel.CreateCell(0);
                excel.WriteCell("主体名称");
                excel.CreateCell(1);
                excel.WriteCell("主体类型");
                excel.CreateCell(2);
                excel.WriteCell("业务类型");
                excel.CreateCell(3);
                excel.WriteCell("税号国家");
                excel.CreateCell(4);
                excel.WriteCell("注册邮箱");
                for (int j = 0; j < cellName.Count; j++)
                {
                    excel.CreateCell(j + 5);
                    excel.WriteCell(cellName[j]);
                }
            }

            excel.Save(stream);

            return stream;
        }

        #region 对接第三方平台
        /// <summary>
        /// 查询第三方平台税号的待申报订单，（用于下载销售数据）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<List<SalesDataDownloadModel>> GetThirdPartyDeclaredOrders(AgentToDeclaredRequest request)
        {
            var taxnumbers = await _taxNumberRepository.GetListAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                .And(new MatchTaxNumberByIsAbleDeclareSpecification(true))
               .And(new MatchTaxNumberBySourceCodeSpecification(request.SourceCode)));
            var declaredOrders = await _declaredOrderRepository.GetListAsync(new MatchDeclaredOrderByIsDeleteSpecification(false)
                .And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare))
               .And(new MatchDeclaredOrderBySourceCodeSpecification(request.SourceCode)));
            var bindshopTaxnos = taxnumbers.Where(t => t.StoreAuthorizationList != null && t.StoreAuthorizationList.Any() && !t.TaxNumberEntity.VatNo.IsNullOrBlank())
                 .Select(t => t.TaxNumberEntity.VatNo).ToList();
            var needDownloadOrders = declaredOrders.Where(d => bindshopTaxnos.Contains(d.TaxNumberInfo.TaxNumberEntity.VatNo))
                  .Select(d => new { d.TaxNumberInfo.TaxNumberEntity.VatNo, d.DeclarationCycle });

            List<SalesDataDownloadModel> result = new List<SalesDataDownloadModel>();
            foreach (var item in needDownloadOrders)
            {
                var tax = taxnumbers.FirstOrDefault(t => t.TaxNumberEntity.VatNo == item.VatNo);
                SalesDataDownloadModel model = new SalesDataDownloadModel()
                {
                    ShopId = tax?.StoreAuthorizationList.FirstOrDefault()?.SellerId,
                    TaxNo = item.VatNo,
                    StartDate = item.DeclarationCycle.StartTime.Value.ToLocalTime().ToString("yyyy-MM-dd"),
                    EndDate = item.DeclarationCycle.EndTime.Value.ToLocalTime().ToString("yyyy-MM-dd")
                };
                result.Add(model);
            }
            return result;
        }


        #endregion
        public async Task<Stream> ExportTemporaryInfo()
        {

            var taxnumbers = (await _taxNumberRepository.GetListAsync(new MatchTaxNumerByIsDeletedSpecification(false).And(new MatchTaxNumberByIsTesterSpecification(false)))).ToList().OrderByDescending(p => p.CreateTime);

            var data = taxnumbers.Distinct(p => p.CompanyInfo.CnName).Select(p => new ExportTaxInfoModel() { TaxNo = p.TaxType == "VAT" ? p.TaxNumberEntity.VatNo : p.IOSSCode, AgentManagerId = p.AgentManagerId, CountryName = p.TaxCountry.CnName, ManagerCode = p.ManagerCode, CustomerManager = p.CustomerServiceManager?.Name, SaleManager = p.SaleClientManager?.Name, CnName = p.CompanyInfo?.CnName, EnName = p.CompanyInfo?.EnName }).ToList();
            IExporter exporter = new ExcelExporter();
            var bytes = await exporter.ExportAsByteArray(data);

            var stream = new MemoryStream(bytes);
            return stream;
        }

        private string ConverteStatus(string status)
        {
            switch (status)
            {
                case TaxNumberStatus.UnAuthorize:
                    return "待授权";
                case TaxNumberStatus.Authorizing:
                    return "授权中";
                case TaxNumberStatus.Authorized:
                    return "已授权";
                case TaxNumberStatus.Revoke:
                    return "已解除授权";
                case TaxNumberStatus.Stoping:
                    return "申请停用中";
                case TaxNumberStatus.Stopped:
                    return "已停用";
                case TaxNumberStatus.Activing:
                    return "申请启用中";
                case TaxNumberStatus.Canceling:
                    return "申请注销中";
                case TaxNumberStatus.Canceled:
                    return "已注销";
                default:
                    return "";
            }
        }

        /// <summary>
        /// 按用户分组统计产品订单数据
        /// </summary>
        /// <param name="param">统计查询参数</param>
        /// <returns>按用户分组的统计数据</returns>
        public async Task<List<ProductOrderStatisticsModel>> GetProductOrderStatistics(ProductOrderStatisticsParam param)
        {
            // 创建结果对象
            var result = new List<ProductOrderStatisticsModel>();
            // {
            //     TotalOrderCount = 0,
            //     TotalReceiveAmount = new Domain.Common.Models.Money(0, "CNY"),
            //     TotalExpenseAmount = new Domain.Common.Models.Money(0, "CNY"),
            //     TotalUnfollowedCustomerCount = 0,
            //     Items = new List<ProductOrderStatisticsModel>()
            // };

            try
            {
                // 构建查询条件
                var isDeleteSpec = new Domain.AggregatesModel.CrmAgg.Specifications.MatchProductOrderByIsDeleteSpecification(false);

                // 创建基础查询条件
                ISpecification<Domain.AggregatesModel.CrmAgg.ProductOrder> specification = isDeleteSpec;

                // 添加时间范围条件 - 使用Eval方法
                specification = specification.And(
                    Specification<Domain.AggregatesModel.CrmAgg.ProductOrder>.Eval(
                        p => p.CreateTime >= param.StartTime && p.CreateTime <= param.EndTime.AddDays(1).AddSeconds(-1)
                    )
                );

                // 如果提供了创建人姓名，添加创建人条件
                if (!string.IsNullOrWhiteSpace(param.CreatorName))
                {
                    specification = specification.And(
                        Specification<Domain.AggregatesModel.CrmAgg.ProductOrder>.Eval(
                            p => p.CreateUser != null && p.CreateUser.Contains(param.CreatorName)
                        )
                    );
                }

                // 获取所有符合条件的产品订单
                var productOrders = await _productOrderRepository.GetListAsync(specification);
                if (productOrders == null || !productOrders.Any())
                {
                    return result;
                }

                // 获取所有相关的客户ID
                var customerIds = productOrders.Select(p => p.CustomerId).Distinct().ToList();

                // 查询相关的客户信息
                var customerSpec = Specification<Customer>.Eval(
                    c => customerIds.Contains(c.Id)
                );
                var customers = await _customerRepository.GetListAsync(customerSpec);

                // 如果指定了客户类型，根据客户类型过滤客户列表
                if (!string.IsNullOrEmpty(param.CustomerType))
                {
                    var customerIdsWithType = customers
                        .Where(c => c.Type != null && c.Type == param.CustomerType)
                        .Select(c => c.Id)
                        .ToList();

                    // 根据过滤后的客户ID列表，过滤产品订单
                    productOrders = productOrders.Where(p => customerIdsWithType.Contains(p.CustomerId)).ToList();

                    // 如果过滤后没有订单，直接返回空结果
                    if (!productOrders.Any())
                    {
                        return result;
                    }
                }

                // 按用户分组统计
                var groupedOrders = productOrders.GroupBy(p => p.CreateUserCode);

                // 获取过滤后的产品订单ID列表和客户ID列表
                var filteredProductOrderIds = productOrders.Select(p => p.Id).ToList();
                var filteredCustomerIds = productOrders.Select(p => p.CustomerId).Distinct().ToList();

                // 查询所有相关的支付记录 - 只通过客户ID过滤
                var allPayments = await _crmPaymentRepository.GetListAsync(
                    Specification<CrmPayment>.Eval(
                        p => filteredCustomerIds.Contains(p.CustomerId)
                    )
                );


                // 查询所有相关的支出记录 - 只通过客户ID过滤
                var allExpenses = await _expenseRepository.GetListAsync(
                    Specification<Expense>.Eval(
                        e => filteredCustomerIds.Contains(e.CustomerId)
                    )
                );

                // 查询在指定时间范围内的跟进记录
                var customerFollowupSpec = new Domain.AggregatesModel.CrmAgg.Specifications.MatchFollowupByIsDeleteSpecification(false)
                    .And(new Domain.AggregatesModel.CrmAgg.Specifications.MatchFollowupByTimeRangeSpecification(param.StartTime, param.EndTime));

                var followups = await _followupRepository.GetListAsync(customerFollowupSpec);


                // 按用户分组处理数据
                foreach (var group in groupedOrders)
                {
                    var userCode = group.Key;
                    var userName = group.First().CreateUser;
                    var orderCount = group.Count();

                    // 获取该用户的产品订单ID列表、客户ID列表和产品代码列表
                    var userProductOrderIds = group.Select(p => p.Id).ToList();
                    var userCustomerIds = group.Select(p => p.CustomerId).Distinct().ToList();
                    var userProductCodes = group.Select(p => p.ProductCode).Distinct().ToList();

                    // 计算该用户的收款金额 - 通过客户ID匹配出对应的收款记录
                    var userPayments = allPayments.Where(p => userCustomerIds.Contains(p.CustomerId)).ToList();
                    var receiveAmount = new Domain.Common.Models.Money(0, "CNY");
                    if (userPayments.Any())
                    {
                        decimal totalAmount = 0;
                        foreach (var payment in userPayments)
                        {
                            if (payment.PaymentDetails != null)
                            {
                                foreach (var detail in payment.PaymentDetails)
                                {
                                    // 检查产品明细是否与用户的产品订单相关: 系统中ProductName=ProductCode
                                    if (detail.Products != null)
                                    {
                                        var relatedProducts = detail.Products.Where(product => userProductCodes.Contains(product.ProductName));
                                        totalAmount += relatedProducts.Sum(product => product.PaymentAmount.Amount);
                                    }
                                }
                            }
                        }
                        receiveAmount = new Domain.Common.Models.Money(totalAmount, "CNY");
                    }

                    // 计算该用户的支出金额 - 通过客户ID匹配出对应的支出记录
                    var userExpenses = allExpenses.Where(p => userCustomerIds.Contains(p.CustomerId)).ToList();
                    var expenseAmount = new Domain.Common.Models.Money(0, "CNY");
                    if (userExpenses.Any())
                    {
                        decimal totalAmount = 0;
                        foreach (var expense in userExpenses)
                        {
                            if (expense.ExpenseDetails != null)
                            {
                                foreach (var detail in expense.ExpenseDetails)
                                {
                                    // 检查产品明细是否与用户的产品订单相关
                                    if (detail.Products != null)
                                    {
                                        foreach (var product in detail.Products)
                                        {
                                            // 检查产品明细是否与用户的产品订单相关: 系统中ProductName=ProductCode
                                            if (detail.Products != null)
                                            {
                                                var relatedProducts = detail.Products.Where(product => userProductCodes.Contains(product.ProductName));
                                                totalAmount += relatedProducts.Sum(product => product.ExpenseAmount.Amount);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        expenseAmount = new Domain.Common.Models.Money(totalAmount, "CNY");
                    }

                    // 计算该用户的未跟进客户数 - 查询当前订单对应的客户在查询的时间段内是否有跟进记录
                    var unfollowedCustomerCount = 0;

                    // 获取该用户关联的客户列表
                    var userRelatedCustomers = customers.Where(c => userCustomerIds.Contains(c.Id)).ToList();


                    // 计算未跟进的客户数量
                    unfollowedCustomerCount = userRelatedCustomers.Count(customer =>
                        !followups.Any(followup => followup.CustomerId == customer.Id));


                    // 创建用户统计模型
                    var statisticsModel = new ProductOrderStatisticsModel
                    {
                        UserName = userName,
                        UserCode = userCode,
                        OrderCount = orderCount,
                        ReceiveAmount = receiveAmount,
                        ExpenseAmount = expenseAmount,
                        UnfollowedCustomerCount = unfollowedCustomerCount
                    };

                    // 添加到结果列表
                    result.Add(statisticsModel);

                    // 累加总计数据
                    // result.TotalOrderCount += orderCount;
                    // result.TotalReceiveAmount = new Domain.Common.Models.Money(
                    //     result.TotalReceiveAmount.Amount + receiveAmount.Amount,
                    //     result.TotalReceiveAmount.CurrencyCode
                    // );
                    // result.TotalExpenseAmount = new Domain.Common.Models.Money(
                    //     result.TotalExpenseAmount.Amount + expenseAmount.Amount,
                    //     result.TotalExpenseAmount.CurrencyCode
                    // );
                    // result.TotalUnfollowedCustomerCount += unfollowedCustomerCount;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取产品订单统计数据时发生错误");
            }

            return result;
        }
    }
}
