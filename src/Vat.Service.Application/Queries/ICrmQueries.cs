using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Vat.Service.Application.DTO.Common;
using Vat.Service.Application.ViewModels.Crm;
using Vat.Service.Application.ViewModels.Crm.Request;
using Vat.Service.Application.Models.Crm;

namespace Vat.Service.Application.Queries
{
    public interface ICrmQueries
    {
        Task<PageResult<CustomerModel>> GetCustomerPageList(CustomerParam param);
        Task<CustomerModel> GetCustomerDetail(string id);
        Task<List<CustomerItem>> GetCustomerItems(string key);
        Task<Stream> ExportCustomer(CustomerParam param);
        Task<PageResult<ChannelModel>> GetChannelPageList(ChannelParam param);
        Task<ChannelModel> GetChannelDetail(string id);
        Task<List<SelectItem>> GetChannelItems(string key);
        Task<Stream> ExportChannel(ChannelParam param);
        Task<PageResult<ProjectModel>> GetProjectPageList(ProjectParam param);
        Task<ProjectModel> GetProjectDetail(string id);
        Task<List<SelectItem>> GetProjectItems(string key);
        Task<Stream> ExportProject(ProjectParam param);
        Task<PageResult<ServiceAgreementModel>> GetServiceAgreementPageList(ServiceAgreementParam param);
        Task<ServiceAgreementModel> GetServiceAgreementDetail(string id);
        Task<string> GetLastestServiceAgreementCode();
        Task<Stream> ExportAgreement(ServiceAgreementParam param);
        Task<PageResult<CrmAgentModel>> GetCrmAgentPageList(CrmAgentParam param);
        Task<CrmAgentModel> GetCrmAgentDetail(string id);
        Task<List<SelectItem>> GetCrmAgentItems(string key);
        Task<Stream> ExportCrmAgent(CrmAgentParam param);
        Task<PageResult<CrmCustomerModel>> GetCrmCustomerPageList(CrmCustomerParam param);
        Task<CrmCustomerModel> GetCrmCustomerDetail(string id);
        Task<List<SelectItem>> GetAgentItems(string key);
        Task<PageResult<FollowupModel>> GetFollowupPageList(FollowupParam param);
        Task<PageResult<ProductOrderModel>> GetProductOrderPageList(ProductOrderParam param);
        Task<ProductOrderModel> GetProductOrderDetail(string id);
        Task<List<ProductOrderHandoverModel>> GetProductOrderHandoverList(string productOrderId);

        /// <summary>
        /// 获取产品订单交付记录列表
        /// </summary>
        Task<List<Models.Crm.ProductOrderDeliveryModel>> GetProductOrderDeliveryList(string productOrderId);

        Task<List<ApproverModel>> GetApproverList();
        Task<PageResult<PaymentModel>> GetPaymentPageList(PaymentParam param);
        Task<PaymentModel> GetPaymentDetail(string id);
        Task<PageResult<ExpenseModel>> GetExpensePageList(ExpenseParam param);
        Task<ExpenseModel> GetExpenseDetail(string id);

        // 公海相关查询
        Task<PageResult<PublicPoolRuleModel>> GetPublicPoolRulePageList(PublicPoolRuleParam param);
        Task<PublicPoolRuleModel> GetPublicPoolRuleDetail(string id);
        Task<PageResult<CustomerModel>> GetPublicPoolCustomerPageList(CustomerParam param);
    }
}
