using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Vat.Service.Application.Services
{
    /// <summary>
    /// 公海后台服务 - 定时执行自动归属公海逻辑
    /// </summary>
    public class PublicPoolBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PublicPoolBackgroundService> _logger;
        private readonly TimeSpan _period = TimeSpan.FromHours(1); // 每小时执行一次

        public PublicPoolBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<PublicPoolBackgroundService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("公海后台服务已启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var publicPoolService = scope.ServiceProvider.GetRequiredService<PublicPoolService>();
                        await publicPoolService.AutoMoveToPublicPool();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行公海自动归属任务时发生错误");
                }

                // 等待下一次执行
                await Task.Delay(_period, stoppingToken);
            }

            _logger.LogInformation("公海后台服务已停止");
        }
    }
}
