using System;
using System.Collections.Generic;
using System.Text;
using AngleSharp.Html.Parser;
using AngleSharp.Html.Dom;
using BFE.Framework.Infrastructure.Crosscutting.Json;
using System.Net;
using System.Threading.Tasks;
using Vat.Service.Application.Common.Http;
using System.IO;
using CsvHelper;
using System.Linq;
using Magicodes.ExporterAndImporter.Excel;
using Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg.Specifications;
using BFE.Framework.Domain.Core.Specification;
using Vat.Service.Domain.Repositories.VatExchangeRate;
using Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg;
using Spire.Xls;
using Vat.Service.Application.DataTransferModels.ExchangeRate;
using Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg.SpecificationsForVATExchangeRateByDay;
using Vat.Service.Application.Infrastructure.Job;
using Vat.Service.Domain.Repositories.ExchangeRateLog;
using BFE.Framework.Domain.Core;
using Vat.Service.Domain.AggregatesModel.ExchangeRateLogAgg;
using Vat.Service.Domain.Constants.Type;
using Vat.Service.Domain.AggregatesModel.ExchangeRateLogAgg.Specifications;
using Vat.Service.Application.Jobs.ExchangeRateJobs;

namespace Vat.Service.Application.Services.Impl.ExchangeRates
{
    public class ExchangeRateServiceBase
    {
        private readonly IJsonConverter _jsonConverter;

        private readonly IVatExchangeRateRepository _vatExchangeRateRepository;

        private readonly IVatExchangeRateByDayRepository _vatExchangeRateByDayRepository;

        private readonly IJobSchedule _jobSchedule;

        private readonly IExchangeRateLogRepository _exchangeRateLogRepository;

        private readonly IDBContext _dBContext;

        public ExchangeRateServiceBase(IJsonConverter jsonConverter, IVatExchangeRateByDayRepository vatExchangeRateByDayRepository, IJobSchedule jobSchedule, IExchangeRateLogRepository exchangeRateLogRepository, IDBContext dBContext, IVatExchangeRateRepository vatExchangeRateRepository)
        {
            _jsonConverter = jsonConverter;
            _vatExchangeRateByDayRepository = vatExchangeRateByDayRepository;
            _jobSchedule = jobSchedule;
            _exchangeRateLogRepository = exchangeRateLogRepository;
            _dBContext = dBContext;
            _vatExchangeRateRepository = vatExchangeRateRepository;
        }

        public ExchangeRateServiceBase(IJsonConverter jsonConverter, IVatExchangeRateRepository vatExchangeRateRepository, IJobSchedule jobSchedule, IExchangeRateLogRepository exchangeRateLogRepository, IDBContext dBContext)
        {
            _jsonConverter = jsonConverter;
            _vatExchangeRateRepository = vatExchangeRateRepository;
            _jobSchedule = jobSchedule;
            _exchangeRateLogRepository = exchangeRateLogRepository;
            _dBContext = dBContext;
        }
        /// <summary>
        /// 转换
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="json"></param>
        /// <returns></returns>
        protected T GetEntity<T>(string json)
        {
            return _jsonConverter.DeserializeObject<T>(json);
        }
        /// <summary>
        /// 通过官方地址来得到Html代码
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        protected IHtmlDocument GetIHtmlDocmentByPathAddress(string path)
        {
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls13 | SecurityProtocolType.Tls12;
            WebClient wc = new WebClient();
            wc.BaseAddress = path;
            wc.Encoding = Encoding.UTF8;
            wc.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            wc.Headers.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            wc.Headers.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            var html = wc.DownloadString("");
            var parser = new HtmlParser();
            return parser.ParseDocument(html);
        }

        /// <summary>
        /// 通过csv来获取文件
        /// </summary>
        /// <typeparam name="T">Csv文件获取后的载体</typeparam>
        /// <typeparam name="TMap">Csv和载体映射类</typeparam>
        /// <param name="filePath">Csv文件的路径</param>
        /// <param name="regionString">区域相关间隔符关键字</param>
        /// <returns></returns>
        protected async Task<List<T>> UseCsv<T, TMap>(string filePath, string regionString = "zh-CHS", bool isZip = false) where T : class where TMap : class
        {
            var sourcefilePath = GetSourcePath(filePath);
            var records = new List<T>();
            CsvReader csvReader = null;
            using (TextReader reader = new StreamReader(sourcefilePath.Value))
            {
                if (regionString == "pl-PL")
                {
                    csvReader = new CsvReader(reader, new CsvHelper.Configuration.CsvConfiguration(new System.Globalization.CultureInfo(regionString)) { Delimiter = ";" });

                }
                else
                {
                    csvReader = new CsvReader(reader, new System.Globalization.CultureInfo(regionString));
                }

                csvReader.Context.RegisterClassMap(typeof(TMap));
                records = csvReader.GetRecords<T>().ToList();
            }
            System.IO.File.Delete(sourcefilePath.Value);
            return records;
        }

        private KeyValuePair<string, string> GetSourcePath(string filePath)
        {
            string fileExt = Path.GetExtension(filePath);//这里是后缀
            if (fileExt.Contains(".xlsx"))
            {
                fileExt = ".xlsx";
            }
            else if (fileExt.Contains(".xls"))
            {
                fileExt = ".xls";
            }
            string saveName = Guid.NewGuid().ToString("N") + fileExt;
            var pathDic = Directory.GetCurrentDirectory();
            string sourcePath = Path.Combine(pathDic, "StaticFiles");
            if (!Directory.Exists(sourcePath))
            {
                Directory.CreateDirectory(sourcePath);
            }
            var sourcefilePath = Path.Combine(sourcePath, saveName);
            return new KeyValuePair<string, string>(saveName, sourcefilePath);
        }

        /// <summary>
        /// 通过Excel来获取文件（简单）
        /// </summary>
        /// <typeparam name="T">Excel类</typeparam>
        /// <param name="filePath"></param>
        /// <returns></returns>
        protected async Task<List<T>> UseExcel<T>(string filePath) where T : class, new()
        {
            var sourceFile = GetSourcePath(filePath);
            var stream = await DownloadFileClient.GetFile(filePath);
            var path = string.Empty;
            //保存文件
            if (sourceFile.Key.Contains(".xls") && !sourceFile.Key.Contains(".xlsx"))
            {
                Workbook workbook = new Workbook();
                workbook.LoadFromStream(stream);
                workbook.SaveToFile($"StaticFiles/{sourceFile.Key.Replace(".xls", ".xlsx")}", ExcelVersion.Version2013);
                path = sourceFile.Value.Replace(".xls", ".xlsx");
            }
            else
            {
                using (var fileStream = new FileStream(sourceFile.Value, FileMode.Create))
                {
                    await stream.CopyToAsync(fileStream);
                    path = sourceFile.Value;
                }
            }

            IExcelImporter importer = new ExcelImporter();
            var result = await importer.Import<T>(path);
            File.Delete(path);
            path = path.Replace(".xlsx", "_.xlsx");
            if (File.Exists(path))
            {
                File.Delete(path);
            }
            return result.Data.ToList();
        }

        /// <summary>
        /// 检查汇率是否存在
        /// </summary>
        /// <param name="vatCountryCurrency"></param>
        /// <param name="countryCode"></param>
        /// <param name="exchangeCurrency"></param>
        /// <param name="month"></param>
        /// <returns></returns>
        protected async Task<VatExchangeRate> GetExchangeRate(string vatCountryCurrency, string countryCode, string exchangeCurrency, DateTime month)
        {
            ISpecification<Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg.VatExchangeRate> specification = new MatchVatExchangeRateByIsDeletedSpecification(false);
            specification = specification.And(new MatchVatExchangeRateByVatCountryCurrencySpecification(vatCountryCurrency));
            specification = specification.And(new MatchVatExchangeRateByVatCountryCodeSpecification(countryCode));
            specification = specification.And(new MatchVatExchangeRateByExchangeCurrencySpecification(exchangeCurrency));
            specification = specification.And(new MatchVatExchangeRateByMonthSpecification(month));

            return await _vatExchangeRateRepository.GetAsync(specification);
        }

        /// <summary>
        /// 检查汇率是否存在
        /// </summary>
        /// <param name="vatCountryCurrency"></param>
        /// <param name="countryCode"></param>
        /// <param name="exchangeCurrency"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        protected async Task<VatExchangeRateByDay> GetExchangeRateByDay(string vatCountryCurrency, string countryCode, string exchangeCurrency, DateTime date)
        {
            ISpecification<Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg.VatExchangeRateByDay> specification = new MatchVatExchangeRateByDayByIsDeletedSpecification(false);
            specification = specification.And(new MatchVatExchangeRateByDayByVatCountryCurrencySpecification(vatCountryCurrency));
            specification = specification.And(new MatchVatExchangeRateByDayByVatCountryCodeSpecification(countryCode));
            specification = specification.And(new MatchVatExchangeRateByDayByExchangeCurrencySpecification(exchangeCurrency));
            specification = specification.And(new MatchVatExchangeRateByDayByDaySpecification(date));

            return await _vatExchangeRateByDayRepository.GetAsync(specification);
        }

        /// <summary>
        /// 重复操作任务
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        protected async Task DoRetryOperation(string type, string message = "目前采集的对象没有获取到")
        {
            _dBContext.BeginTransaction();
            TimeSpan timeSpan = TimeSpan.FromHours(3);
            ExchangeRateLog failedLog = null;
            if (type == ExchangeRateType.EURO || type == ExchangeRateType.POUND || type == ExchangeRateType.EUROByDay)
            {
                failedLog = await _exchangeRateLogRepository.GetAsync(new MatchExchangeRateLogByPullDateAndTypeSpecification($"{DateTime.Now.AddMonths(-1).ToString("yyyy-MM")}", type, false));
                timeSpan = (DateTime.Now.AddDays(1) - DateTime.Now);
            }
            else if (type == ExchangeRateType.PLN)
            {
                failedLog = await _exchangeRateLogRepository.GetAsync(new MatchExchangeRateLogByPullDateAndTypeSpecification($"{DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd")}", type, false));
            }

            if (failedLog != null)
            {
                if (failedLog.RetryCount < 6)
                {
                    failedLog.AddRetryCount();
                    await _exchangeRateLogRepository.UpdateAsync(failedLog);
                }
                else
                {
                    return;
                }
            }
            else
            {
                var log = new ExchangeRateLog(type, false, message, $"{DateTime.Now.AddMonths(-1).ToString("yyyy-MM")}", DateTime.Now);
                if (type == ExchangeRateType.PLN)
                {
                    log.SetPullDate(DateTime.Now.AddDays(-1).Date.ToString("yyyy-MM-dd"));
                }
                await _exchangeRateLogRepository.AddAsync(log);
            }
            await _dBContext.CommitAsync();
            switch (type)
            {
                case ExchangeRateType.EURO:
                    _jobSchedule.DelayedJob(new EuroExchangeRateDto(), timeSpan);
                    break;
                case ExchangeRateType.PLN:
                    _jobSchedule.DelayedJob(new PlnExchangeRateDto(), timeSpan);
                    break;
                case ExchangeRateType.POUND:
                    _jobSchedule.DelayedJob(new PoundExchangeRateDto(), timeSpan);
                    break;
                case ExchangeRateType.EUROByDay:
                    _jobSchedule.DelayedJob(new EuroExchangeRateByDayDto(), timeSpan);
                    break;
                default:
                    break;
            }
        }
    }
}
