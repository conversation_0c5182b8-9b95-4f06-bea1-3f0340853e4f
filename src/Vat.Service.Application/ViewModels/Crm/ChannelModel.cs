﻿using System;
using System.Collections.Generic;
using System.Text;
using Vat.Service.Application.Common.Model;

namespace Vat.Service.Application.ViewModels.Crm
{
    public class ChannelModel
    {
        public string Id { get; set; }
        /// <summary>
        /// 编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string Telephone { get; set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 佣金比例
        /// </summary>
        public decimal CommissionRate { get; set; }
        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; set; }
        /// <summary>
        /// 角色名称
        /// </summary>
        public string Role { get; set; }
        /// <summary>
        /// 联系方式
        /// </summary>
        public string ContactType { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string ModifyUser { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }
    }
}
