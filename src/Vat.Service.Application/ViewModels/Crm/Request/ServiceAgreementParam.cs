﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Vat.Service.Application.ViewModels.Crm.Request
{
    public class ServiceAgreementParam
    {

        public ServiceAgreementParam()
        {
            this.PageNumber = 1;
            this.PageSize = 20;
        }
        /// <summary>
        /// 关键字
        /// </summary>
        public string KeyWord { get; set; }
        /// <summary>
        /// 跟进状态
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 客户类型
        /// </summary>
        public string CustomerType { get; set; }
        /// <summary>
        /// 客户code
        /// </summary>
        public string CustomerCode { get; set; }
        /// <summary>
        /// 客户来源
        /// </summary>
        public string CustomerSource { get; set; }
        /// <summary>
        /// 协议编号
        /// </summary>
        public string AgreementCode { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public string CompanyId { get; set; }
        /// <summary>
        /// 国家
        /// </summary>
        public string CountryCode { get; set; }
        /// <summary>
        /// 业务项目
        /// </summary>
        public string ProjectCode { get; set; }
        /// <summary>
        /// 代理
        /// </summary>
        public string Agent { get; set; }
        /// <summary>
        /// 销售经理
        /// </summary>
        public string Saleman { get; set; }
        /// <summary>
        /// 客服
        /// </summary>
        public string CustomerServer { get; set; }
        /// <summary>
        /// 税务经理
        /// </summary>
        public string TaxManager { get; set; }
        /// <summary>
        /// 签约开始
        /// </summary>
        public DateTime? CreateBeginDate { get; set; }
        /// <summary>
        /// 签约结束
        /// </summary>
        public DateTime? CreateEndDate { get; set; }
        /// <summary>
        /// 合同内容 这里实际是合同内容
        /// </summary>
        public string PartnerCode { get; set; }
        /// <summary>
        /// 计提成本金额
        /// </summary>
        public string CommissionAmount { get; set; }
        /// <summary>
        /// 实际付款金额
        /// </summary>
        public string PaymentAmount { get; set; }
        /// <summary>
        /// 业务合同起始日期
        /// </summary>
        public DateTime? ContractStartDate { get; set; }
        /// <summary>
        /// 业务合同结束日期
        /// </summary>
        public DateTime? ContractEndDate { get; set; }
        /// <summary>
        /// 申报方式
        /// </summary>
        public string DeclareMethod { get; set; }
        /// <summary>
        /// 合同金额
        /// </summary>
        public string ContractAmount { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>
        public string ReceiveAmount { get; set; }
        /// <summary>
        /// 合作方编码
        /// </summary>
        public string Collaborators { get; set; }
        /// <summary>
        /// 对应关联到后端算税和维护板块
        /// </summary>
        public string Correlation { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }
        /// <summary>
        /// 编辑人
        /// </summary>
        public string ModifyUser { get; set; }
        /// <summary>
        /// 渠道
        /// </summary>
        public string Channel { get; set; }

        public int PageNumber { get; set; }


        public int PageSize { get; set; }
        public List<string> CheckIds { get; set; }
    }
}
