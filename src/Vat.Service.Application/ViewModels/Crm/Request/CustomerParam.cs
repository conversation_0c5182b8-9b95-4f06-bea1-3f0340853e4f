﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Vat.Service.Application.ViewModels.Crm.Request
{
    public class CustomerParam
    {

        public CustomerParam()
        {
            this.PageNumber = 1;
            this.PageSize = 20;
        }
        /// <summary>
        /// 关键字
        /// </summary>
        public string KeyWord { get; set; }

        public int PageNumber { get; set; }

        public int PageSize { get; set; }

        /// <summary>
        /// 客户编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 客户中文名称
        /// </summary>
        public string CnName { get; set; }
        /// <summary>
        /// 客户英文名称
        /// </summary>
        public string EnName { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Telephone { get; set; }

        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 客户类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 所属公司
        /// </summary>
        public string AffiliatedCompany { get; set; }

        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建开始时间
        /// </summary>
        public DateTime? CreateBeginDate { get; set; }
        /// <summary>
        /// 创建结束时间
        /// </summary>
        public DateTime? CreateEndDate { get; set; }

        /// <summary>
        /// 编辑人
        /// </summary>
        public string ModifyUser { get; set; }
        /// <summary>
        /// 代理
        /// </summary>
        public string Agent { get; set; }
        /// <summary>
        /// 渠道
        /// </summary>
        public string Channel { get; set; }

        /// <summary>
        /// 是否公海客户
        /// </summary>
        public bool? IsPublicPool { get; set; }

        public List<string> CheckIds { get; set; }
    }
}
