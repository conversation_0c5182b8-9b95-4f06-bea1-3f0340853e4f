﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Vat.Service.Application.ViewModels.Crm.Request
{
    public class ProjectParam
    {

        public ProjectParam()
        {
            this.PageNumber = 1;
            this.PageSize = 20;
        }
        /// <summary>
        /// 关键字
        /// </summary>
        public string KeyWord { get; set; }
        /// <summary>
        /// 国家
        /// </summary>
        public string Country { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 编辑人
        /// </summary>
        public string ModifyUser { get; set; }
        /// <summary>
        /// 创建日期起始
        /// </summary>
        public DateTime CreateBeginDate { get; set; }
        /// <summary>
        /// 创建日期截止
        /// </summary>
        public DateTime CreateEndDate { get; set; }


        public int PageNumber { get; set; }


        public int PageSize { get; set; }
        public List<string> CheckIds { get; set; }
    }
}
