﻿using System;
using System.Collections.Generic;
using System.Text;
using Vat.Service.Application.Common.Model;

namespace Vat.Service.Application.ViewModels.Crm
{
    public class ServiceAgreementModel
    {
        public string Id { get; set; }
        /// <summary>
        /// 协议状态
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 渠道code
        /// </summary>
        public string ChannelCode { get; set; }
        /// <summary>
        /// 渠道名称
        /// </summary>
        public string ChannelName { get; set; }
        /// <summary>
        /// 签约日期
        /// </summary>
        public DateTime? SignDate { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public string CustomerId { get; set; }
        /// <summary>
        /// 客户中文名称
        /// </summary>
        public string CustomerCnName { get; set; }
        /// <summary>
        /// 客户中文名称
        /// </summary>
        public string CustomerEnName { get; set; }
        /// <summary>
        /// 客户类型
        /// </summary>
        public string CustomerType { get; set; }
        /// <summary>
        /// 客户code
        /// </summary>
        public string CustomerCode { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public string CustomerSource { get; set; }
        /// <summary>
        /// 客户所属公司
        /// </summary>
        public string AffiliatedCompany { get; set; }
        /// <summary>
        /// 协议编号
        /// </summary>
        public string AgreementCode { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public string Country { get; set; }
        /// <summary>
        /// 业务Code
        /// </summary>
        public string ProjectCode { get; set; }
        /// <summary>
        /// 业务名称
        /// </summary>
        public string ProjectName { get; set; }
        /// <summary>
        /// 注册类型：
        /// 新注册，转代理，续签，注销
        /// </summary>
        public string RegisterType { get; set; }

        /// <summary>
        /// 业务合同起始日期
        /// </summary>
        public DateTime? ContractStartDate { get; set; }
        /// <summary>
        /// 业务合同结束日期
        /// </summary>
        public DateTime? ContractEndDate { get; set; }
        /// <summary>
        /// 合同金额
        /// </summary>
        public Money ContractAmount { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>
        public Money ReceiveAmount { get; set; }
        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; set; }
        /// <summary>
        /// 客服跟单人
        /// </summary>
        public string CustomerServer { get; set; }
        /// <summary>
        /// 合同内容 这里实际是合同内容
        /// </summary>
        public string PartnerCode { get; set; }
        /// <summary>
        /// 计提成本金额
        /// </summary>
        public Money CommissionAmount { get; set; }
        /// <summary>
        /// 实际付款金额
        /// </summary>
        public Money PaymentAmount { get; set; }
        /// <summary>
        /// 代理
        /// </summary>
        public string Agent { get; set; }
        /// <summary>
        /// 代理名称
        /// </summary>
        public string AgentName { get; set; }
        /// <summary>
        /// 申报方式
        /// 月报，季报，年报，不需要
        /// </summary>
        public string DeclareMethod { get; set; }


        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string ModifyUser { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 协议文件
        /// </summary>
        public string AggrementFile { get; set; }
        /// <summary>
        /// CRM代理
        /// </summary>
        public string CrmAgent { get; set; }
        /// <summary>
        /// 协议文件
        /// </summary>
        public string AggrementFile2 { get; set; }
        /// <summary>
        /// 协议文件
        /// </summary>
        public string AggrementFile3 { get; set; }
        /// <summary>
        /// 税务经理
        /// </summary>
        public string TaxManager { get; set; }
        /// <summary>
        /// 申报状态
        /// </summary>
        public string DeclareStatus { get; set; }
        /// <summary>
        /// 代理地址
        /// </summary>
        public string AgentAdress { get; set; }

        /// <summary>
        /// 法人名称
        /// </summary>
        public string LegalName { get; set; }

        /// <summary>
        /// 合作方编码
        /// </summary>
        public string Collaborators { get; set; }

        /// <summary>
        /// 客户联系人
        /// </summary>
        public string CustomerContacts { get; set; }

        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string CustomerEmail { get; set; }

        /// <summary>
        /// 渠道负责人
        /// </summary>
        public string ChannelCatacts { get; set; }

        /// <summary>
        /// 后端会计
        /// </summary>
        public string BackendAccountant { get; set; }

        /// <summary>
        /// 已开票
        /// </summary>
        public Money InvoicedAmount { get; set; }

        /// <summary>
        /// 未开票
        /// </summary>
        public Money UnInvoicedAmount { get; set; }

        /// <summary>
        /// 已回款
        /// </summary>
        public Money PaidAmount { get; set; }

        /// <summary>
        /// 未回款
        /// </summary>
        public Money UnPaidAmount { get; set; }

        /// <summary>
        /// 市场活动来源
        /// </summary>
        public string MarketSource { get; set; }
        /// <summary>
        /// 对应关联到后端算税和维护板块
        /// </summary>
        public string Correlation { get; set; }
        /// <summary>
        /// 关联项目
        /// </summary>
        public List<ServiceProject> Projects { get; set; }
        /// <summary>
        /// 渠道
        /// </summary>
        public string Channel { get; set; }

    }
}
