using System;
using System.Collections.Generic;
using Vat.Service.Application.Common.Model;

namespace Vat.Service.Application.ViewModels.Crm
{
    public class ProductOrderDeliveryModel
    {
        /// <summary>
        /// 交付记录的唯一标识符
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 关联的产品订单ID
        /// </summary>
        public string ProductOrderId { get; set; }

        /// <summary>
        /// 实际交付日期和时间
        /// </summary>
        public DateTime DeliveryDate { get; set; }

        /// <summary>
        /// 预计完成交付的时间
        /// </summary>
        public DateTime ExpectedCompletionTime { get; set; }

        /// <summary>
        /// 交付情况的备注信息
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 交付相关的文件
        /// </summary>
        public List<FileItem> DeliveryDocument { get; set; }
    }
}