﻿using System;
using System.Collections.Generic;
using System.Text;
using Vat.Service.Application.Common.Model;

namespace Vat.Service.Application.ViewModels.Crm
{
    public class CustomerModel
    {
        public string Id { get; set; }
        /// <summary>
        /// 客户编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 客户中文名称
        /// </summary>
        public string CnName { get; set; }
        /// <summary>
        /// 客户英文名称
        /// </summary>
        public string EnName { get; set; }
        /// <summary>
        /// 联系人（旧）
        /// </summary>
        public string Contacts { get; set; }
        /// <summary>
        /// 联系人（新）
        /// </summary>
        public List<CustomerContact> ContactList { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string Telephone { get; set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 客户来源
        /// </summary>
        public string Source { get; set; }
        /// <summary>
        /// 客户类型
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 联系方式类型
        /// </summary>
        public string ContactType { get; set; }
        /// <summary>
        /// 维易租户
        /// </summary>
        public string TenantCode { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string ModifyUser { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }
        /// <summary>
        /// 所属公司
        /// </summary>
        public string AffiliatedCompany { get; set; }

        /// <summary>
        /// 法人
        /// </summary>
        public string LegalName { get; set; }
        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; set; }
        /// <summary>
        /// 代理
        /// </summary>
        public string Agent { get; set; }
        /// <summary>
        /// 代理名称
        /// </summary>
        public string AgentName { get; set; }
        /// <summary>
        /// 渠道
        /// </summary>
        public string Channel { get; set; }
        /// <summary>
        /// 渠道名称
        /// </summary>
        public string ChannelName { get; set; }
        /// <summary>
        /// 是否公海客户
        /// </summary>
        public bool IsPublicPool { get; set; }
        /// <summary>
        /// 进入公海时间
        /// </summary>
        public DateTime? PublicPoolTime { get; set; }
    }
}
