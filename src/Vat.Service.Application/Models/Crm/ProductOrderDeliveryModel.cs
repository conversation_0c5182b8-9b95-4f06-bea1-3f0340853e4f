﻿using System;
using System.Collections.Generic;
using Vat.Service.Application.Common.Model;

namespace Vat.Service.Application.Models.Crm
{
    public class ProductOrderDeliveryModel
    {
        /// <summary>
        /// 交付记录ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 产品订单ID
        /// </summary>
        public string ProductOrderId { get; set; }

        /// <summary>
        /// 预计完成交付的时间
        /// </summary>
        public DateTime ExpectedCompletionTime { get; set; }

        /// <summary>
        /// 交付相关的文件
        /// </summary>
        public List<FileItem> DeliveryDocuments { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }
    }
}
