using System;
using System.Collections.Generic;
using MediatR;
using Vat.Service.Application.Common.Model;

namespace Vat.Service.Application.Commands.Crm
{
    public class EditProductOrderDeliveryCommand : IRequest<bool>
    {
        /// <summary>
        /// 交付记录的唯一标识符
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 产品订单ID
        /// </summary>
        public string ProductOrderId { get; set; }

        /// <summary>
        /// 预计完成交付的时间
        /// </summary>
        public DateTime ExpectedCompletionTime { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 交付相关的文件
        /// </summary>
        public List<FileItem> DeliveryDocument { get; set; }
    }
}