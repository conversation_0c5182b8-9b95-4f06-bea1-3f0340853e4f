﻿using MediatR;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Vat.Service.Application.Common.Model;
using Vat.Service.Application.Commonds;

namespace Vat.Service.Application.Commands.Crm
{
    public class EditCustomerCommand : IRequest<bool>, ICommand
    {
        public string Id { get; set; }
        /// <summary>
        /// 客户编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 客户中文名称
        /// </summary>
        public string CnName { get; set; }
        /// <summary>
        /// 客户英文名称
        /// </summary>
        public string EnName { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; set; }
        /// <summary>
        /// 联系人(新)
        /// </summary>
        public List<CustomerContact> ContactList { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string Telephone { get; set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 客户来源
        /// </summary>
        public string Source { get; set; }
        /// <summary>
        /// 客户类型
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 联系方式类型
        /// </summary>
        public string ContactType { get; set; }
        /// <summary>
        /// 所属公司
        /// </summary>
        public string AffiliatedCompany { get; set; }
        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; set; }
        /// <summary>
        /// 代理
        /// </summary>
        public string Agent { get; set; }
        /// <summary>
        /// 渠道
        /// </summary>
        public string Channel { get; set; }
    }
}
