using MediatR;

namespace Vat.Service.Application.Commands.Crm
{
    /// <summary>
    /// 添加公海规则命令
    /// </summary>
    public class AddPublicPoolRuleCommand : IRequest<bool>
    {
        /// <summary>
        /// 无跟进天数阈值
        /// </summary>
        public int NoFollowUpDays { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }
    }
}
