﻿using MediatR;
using System;
using System.Text.Json.Serialization;
using Vat.Service.Application.Common.Model;
using Vat.Service.Application.Commonds;

namespace Vat.Service.Application.Commands.Crm
{
    public class EditProjectCommand : IRequest<bool>, ICommand
    {
        public string Id { get; set; }
        /// <summary>
        /// 项目编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 国家
        /// </summary>
        public string Country { get; set; }
    }
}
