using MediatR;

namespace Vat.Service.Application.Commands.Crm
{
    /// <summary>
    /// 从公海领取客户命令
    /// </summary>
    public class ClaimCustomerFromPublicPoolCommand : IRequest<bool>
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 新的销售人员
        /// </summary>
        public string NewSalesman { get; set; }
    }
}
