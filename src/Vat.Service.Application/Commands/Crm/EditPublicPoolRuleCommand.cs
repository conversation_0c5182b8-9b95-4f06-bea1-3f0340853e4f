using MediatR;

namespace Vat.Service.Application.Commands.Crm
{
    /// <summary>
    /// 编辑公海规则命令
    /// </summary>
    public class EditPublicPoolRuleCommand : IRequest<bool>
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 无跟进天数阈值
        /// </summary>
        public int NoFollowUpDays { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyUser { get; set; }
    }
}
