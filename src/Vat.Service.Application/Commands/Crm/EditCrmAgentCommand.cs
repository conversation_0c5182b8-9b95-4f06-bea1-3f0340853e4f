﻿using MediatR;
using System;
using System.Text.Json.Serialization;
using Vat.Service.Application.Common.Model;
using Vat.Service.Application.Commonds;

namespace Vat.Service.Application.Commands.Crm
{
    public class EditCrmAgentCommand : IRequest<bool>, ICommand
    {
        public string Id { get; set; }
        /// <summary>
        /// 项目编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Phone { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; set; }
        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public string Leader { get; set; }
        /// <summary>
        /// 角色名称
        /// </summary>
        public string Role { get; set; }
        /// <summary>
        /// 联系方式
        /// </summary>
        public string ContactType { get; set; }
    }
}
