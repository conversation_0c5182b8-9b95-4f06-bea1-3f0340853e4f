﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using Magicodes.ExporterAndImporter.Excel;
using MediatR;
using Microsoft.Extensions.Logging;
using MWSCustomerService.Model;
using NPOI.Util;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Application.ViewModels.Crm;
using Vat.Service.Common.Model;
using Vat.Service.Common.Util.Excel;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications;
using Vat.Service.Domain.Repositories;
using Vat.Service.Infastructure.Exception;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class ImportCrmAgentCommandHandler : IRequestHandler<ImportCrmAgentCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly ICrmAgentRepository _crmAgentRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<ImportCrmAgentCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public ImportCrmAgentCommandHandler(IDBContext dbContext, ICrmAgentRepository crmAgentRepository, ITypeAdapter typeAdapter, ILogger<ImportCrmAgentCommandHandler> logger, IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _crmAgentRepository = crmAgentRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(ImportCrmAgentCommand request, CancellationToken cancellationToken)
        {
            var file = request.FormFile;
            IExcelImporter Importer = new ExcelImporter();
            var result = new Magicodes.ExporterAndImporter.Core.Models.ImportResult<CrmAgentImportModel>();
            using (Stream stream = file.OpenReadStream())
            {
                result = await Importer.Import<CrmAgentImportModel>(stream);
            }
            if (result.HasError)
            {
                string msg = "";
                if (result.TemplateErrors.Any())
                {
                    var errors = result.TemplateErrors.ToList();
                    msg = $"{string.Join(System.Environment.NewLine, errors.Select(e => $"{e.RequireColumnName}:{e.Message}"))}";
                }
                else if (result.RowErrors.Any())
                {
                    msg = $"{string.Join(System.Environment.NewLine, result.RowErrors.Take(10).Select(e => $"第{e.RowIndex}行:{string.Join(",", e.FieldErrors.Select(f => f.Value))}"))}";
                }
                throw new VatCommonException("ImportError", msg);
            }
            var user = _adminIdentityService.GetAdminUserIdentity();
            var channels = new List<Domain.AggregatesModel.CrmAgg.CrmAgent>();
            foreach (var item in result.Data)
            {
                bool exists = await _crmAgentRepository.ExistsAsync(new MatchCrmAgentByKeyWordSpecification(item.Name));
                if (exists)
                    continue;
                CrmAgent agent = new CrmAgent(item.Code.IsNullOrBlank() ? item.Name : item.Code, item.Name, item.Phone, item.Email, item.Leader, user.FullName, item.Contacts, item.Salesman, "", "");
                channels.Add(agent);
            }
            _dbContext.BeginTransaction();
            await _crmAgentRepository.AddAsync(channels);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
