using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class AddPublicPoolRuleCommandHandler : IRequestHandler<AddPublicPoolRuleCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IPublicPoolRuleRepository _publicPoolRuleRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<AddPublicPoolRuleCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public AddPublicPoolRuleCommandHandler(
            IDBContext dbContext,
            IPublicPoolRuleRepository publicPoolRuleRepository,
            ITypeAdapter typeAdapter,
            ILogger<AddPublicPoolRuleCommandHandler> logger,
            IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _publicPoolRuleRepository = publicPoolRuleRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(AddPublicPoolRuleCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            var createUser = string.IsNullOrEmpty(request.CreateUser) ? user.FullName : request.CreateUser;

            var rule = new PublicPoolRule(
                request.NoFollowUpDays,
                request.IsEnabled,
                createUser);

            _dbContext.BeginTransaction();
            await _publicPoolRuleRepository.AddAsync(rule);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
