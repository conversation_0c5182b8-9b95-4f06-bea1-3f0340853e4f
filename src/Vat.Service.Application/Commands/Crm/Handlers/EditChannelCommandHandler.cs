﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class EditChannelCommandHandler : IRequestHandler<EditChannelCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IChannelRepository _channelRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<EditChannelCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public EditChannelCommandHandler(IDBContext dbContext, IChannelRepository channelRepository, ITypeAdapter typeAdapter, ILogger<EditChannelCommandHandler> logger, IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _channelRepository = channelRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(EditChannelCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            var channel = await _channelRepository.GetByKeyAsync(request.Id);
            channel.Edit(request.Code, request.CompanyName, request.Contacts, request.Telephone, request.Email, request.Address, request.CommissionRate, request.Remark, user.FullName, request.Salesman, request.Role, request.ContactType);
            _dbContext.BeginTransaction();
            await _channelRepository.UpdateAsync(channel);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
