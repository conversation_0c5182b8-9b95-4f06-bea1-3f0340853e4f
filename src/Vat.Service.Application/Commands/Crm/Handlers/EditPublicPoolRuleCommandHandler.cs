using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class EditPublicPoolRuleCommandHandler : IRequestHandler<EditPublicPoolRuleCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IPublicPoolRuleRepository _publicPoolRuleRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<EditPublicPoolRuleCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public EditPublicPoolRuleCommandHandler(
            IDBContext dbContext,
            IPublicPoolRuleRepository publicPoolRuleRepository,
            ITypeAdapter typeAdapter,
            ILogger<EditPublicPoolRuleCommandHandler> logger,
            IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _publicPoolRuleRepository = publicPoolRuleRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(EditPublicPoolRuleCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            var modifyUser = string.IsNullOrEmpty(request.ModifyUser) ? user.FullName : request.ModifyUser;

            var rule = await _publicPoolRuleRepository.GetByKeyAsync(request.Id);
            if (rule == null)
            {
                return false;
            }

            rule.Edit(request.RuleName, request.NoFollowUpDays, request.IsEnabled, modifyUser);

            _dbContext.BeginTransaction();
            await _publicPoolRuleRepository.UpdateAsync(rule);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
