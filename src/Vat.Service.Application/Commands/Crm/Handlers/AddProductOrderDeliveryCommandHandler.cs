﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.Common.Models;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class AddProductOrderDeliveryCommandHandler : IRequestHandler<AddProductOrderDeliveryCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IProductOrderDeliveryRepository _productOrderDeliveryRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly IAdminIdentityService _adminIdentityService;

        public AddProductOrderDeliveryCommandHandler(
            IDBContext dbContext,
            IProductOrderDeliveryRepository productOrderDeliveryRepository,
            ITypeAdapter typeAdapter,
            IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _productOrderDeliveryRepository = productOrderDeliveryRepository;
            _typeAdapter = typeAdapter;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(AddProductOrderDeliveryCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            var deliveryDocuments = new List<FileItem>();
            if (request.DeliveryDocument != null)
            {
                deliveryDocuments = request.DeliveryDocument.Select(item => item.ToDomain()).ToList();
            }

            var productOrderDelivery = new ProductOrderDelivery(
                request.ProductOrderId,
                request.ExpectedCompletionTime,
                deliveryDocuments,
                request.Remark,
                user.FullName);

            _dbContext.BeginTransaction();
            await _productOrderDeliveryRepository.AddAsync(productOrderDelivery);
            await _dbContext.CommitAsync();

            return true;
        }
    }
}
