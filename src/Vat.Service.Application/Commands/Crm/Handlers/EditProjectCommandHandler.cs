﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class EditProjectCommandHandler : IRequestHandler<EditProjectCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IProjectRepository _projectRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<EditProjectCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public EditProjectCommandHandler(IDBContext dbContext, IProjectRepository projectRepository, ITypeAdapter typeAdapter, ILogger<EditProjectCommandHandler> logger, IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _projectRepository = projectRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(EditProjectCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            var project = await _projectRepository.GetByKeyAsync(request.Id);
            project.Edit(request.Name, request.Name, user.FullName, request.Country);
            _dbContext.BeginTransaction();
            await _projectRepository.UpdateAsync(project);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
