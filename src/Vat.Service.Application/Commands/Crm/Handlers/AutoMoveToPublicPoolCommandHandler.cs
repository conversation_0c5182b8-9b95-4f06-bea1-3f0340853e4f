using BFE.Framework.Domain.Core;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class AutoMoveToPublicPoolCommandHandler : IRequestHandler<AutoMoveToPublicPoolCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly ICustomerRepository _customerRepository;
        private readonly IPublicPoolRuleRepository _publicPoolRuleRepository;
        private readonly IFollowupRepository _followupRepository;
        private readonly ILogger<AutoMoveToPublicPoolCommandHandler> _logger;

        public AutoMoveToPublicPoolCommandHandler(
            IDBContext dbContext,
            ICustomerRepository customerRepository,
            IPublicPoolRuleRepository publicPoolRuleRepository,
            IFollowupRepository followupRepository,
            ILogger<AutoMoveToPublicPoolCommandHandler> logger)
        {
            _dbContext = dbContext;
            _customerRepository = customerRepository;
            _publicPoolRuleRepository = publicPoolRuleRepository;
            _followupRepository = followupRepository;
            _logger = logger;
        }

        public async Task<bool> Handle(AutoMoveToPublicPoolCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始执行自动归属公海任务");

                // 获取启用的公海规则
                var enabledRules = await _publicPoolRuleRepository.GetListAsync(
                    new MatchPublicPoolRuleByIsEnabledSpecification(true));

                if (!enabledRules.Any())
                {
                    _logger.LogInformation("没有启用的公海规则，跳过执行");
                    return true;
                }

                // 获取所有非公海客户，排除已成交客户
                var nonPublicPoolCustomers = await _customerRepository.GetListAsync(
                    new MatchCustomerByPublicPoolSpecification(false)
                    .And(new MatchCustomerByIsDeleteSpecification(false))
                    .And(new MatchCustomerByNotTypeSpecification("成交客户")));

                var movedCount = 0;

                foreach (var customer in nonPublicPoolCustomers)
                {
                    // 检查客户是否满足任何一个公海规则
                    foreach (var rule in enabledRules)
                    {
                        if (await ShouldMoveToPublicPool(customer, rule))
                        {
                            customer.MoveToPublicPool();
                            await _customerRepository.UpdateAsync(customer);
                            movedCount++;
                            _logger.LogInformation($"客户 {customer.CnName}({customer.Code}) 已移入公海，规则：{rule.NoFollowUpDays}天未跟进");
                            break; // 满足一个规则即可，不需要检查其他规则
                        }
                    }
                }

                await _dbContext.CommitAsync();
                _logger.LogInformation($"自动归属公海任务完成，共移入 {movedCount} 个客户");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行自动归属公海任务时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 判断客户是否应该移入公海
        /// </summary>
        /// <param name="customer">客户</param>
        /// <param name="rule">公海规则</param>
        /// <returns></returns>
        private async Task<bool> ShouldMoveToPublicPool(Customer customer, PublicPoolRule rule)
        {
            try
            {
                // 获取客户的最后跟进记录
                var lastFollowup = await GetLastFollowupByCustomer(customer.Id);

                DateTime lastFollowupDate;
                if (lastFollowup != null)
                {
                    lastFollowupDate = lastFollowup.CreateTime;
                }
                else
                {
                    // 如果没有跟进记录，使用客户创建时间
                    lastFollowupDate = customer.CreateTime;
                }

                // 计算距离最后跟进的天数
                var daysSinceLastFollowup = (DateTime.Now - lastFollowupDate).Days;

                // 判断是否超过规则设定的天数
                return daysSinceLastFollowup >= rule.NoFollowUpDays;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"判断客户 {customer.Code} 是否应该移入公海时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 获取客户的最后跟进记录
        /// </summary>
        /// <param name="customerId">客户ID</param>
        /// <returns></returns>
        private async Task<Followup> GetLastFollowupByCustomer(string customerId)
        {
            try
            {
                var followups = await _followupRepository.GetListAsync(
                    new MatchFollowupByCustomerIdSpecification(customerId)
                    .And(new MatchFollowupByIsDeleteSpecification(false)));

                return followups.OrderByDescending(f => f.CreateTime).FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取客户 {customerId} 的最后跟进记录时发生错误");
                return null;
            }
        }
    }
}
