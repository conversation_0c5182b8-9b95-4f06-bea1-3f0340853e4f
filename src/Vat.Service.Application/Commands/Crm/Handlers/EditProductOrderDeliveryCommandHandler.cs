﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.Common.Models;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class EditProductOrderDeliveryCommandHandler : IRequestHandler<EditProductOrderDeliveryCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IProductOrderDeliveryRepository _productOrderDeliveryRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly IAdminIdentityService _adminIdentityService;

        public EditProductOrderDeliveryCommandHandler(
            IDBContext dbContext,
            IProductOrderDeliveryRepository productOrderDeliveryRepository,
            ITypeAdapter typeAdapter,
            IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _productOrderDeliveryRepository = productOrderDeliveryRepository;
            _typeAdapter = typeAdapter;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(EditProductOrderDeliveryCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            var delivery = await _productOrderDeliveryRepository.GetByKeyAsync(request.Id);
            if (delivery == null)
            {
                return false;
            }

            var deliveryDocuments = new List<FileItem>();
            if (request.DeliveryDocument != null)
            {
                deliveryDocuments = request.DeliveryDocument.Select(item => item.ToDomain()).ToList();
            }

            delivery.Edit(
                request.ExpectedCompletionTime,
                deliveryDocuments,
                request.Remark,
                user.FullName);

            _dbContext.BeginTransaction();
            await _productOrderDeliveryRepository.UpdateAsync(delivery);
            await _dbContext.CommitAsync();

            return true;
        }
    }
}
