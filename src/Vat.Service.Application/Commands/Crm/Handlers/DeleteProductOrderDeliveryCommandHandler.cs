using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class DeleteProductOrderDeliveryCommandHandler : IRequestHandler<DeleteProductOrderDeliveryCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IProductOrderDeliveryRepository _productOrderDeliveryRepository;
        private readonly IAdminIdentityService _adminIdentityService;

        public DeleteProductOrderDeliveryCommandHandler(
            IDBContext dbContext,
            IProductOrderDeliveryRepository productOrderDeliveryRepository,
            IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _productOrderDeliveryRepository = productOrderDeliveryRepository;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(DeleteProductOrderDeliveryCommand request, CancellationToken cancellationToken)
        {
            var delivery = await _productOrderDeliveryRepository.GetByKeyAsync(request.Id);
            if (delivery == null)
            {
                return false;
            }

            delivery.Delete();

            _dbContext.BeginTransaction();
            await _productOrderDeliveryRepository.UpdateAsync(delivery);
            await _dbContext.CommitAsync();

            return true;
        }
    }
}
