using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class ClaimCustomerFromPublicPoolCommandHandler : IRequestHandler<ClaimCustomerFromPublicPoolCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly ICustomerRepository _customerRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<ClaimCustomerFromPublicPoolCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public ClaimCustomerFromPublicPoolCommandHandler(
            IDBContext dbContext,
            ICustomerRepository customerRepository,
            ITypeAdapter typeAdapter,
            ILogger<ClaimCustomerFromPublicPoolCommandHandler> logger,
            IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _customerRepository = customerRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(ClaimCustomerFromPublicPoolCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            var newSalesman = string.IsNullOrEmpty(request.NewSalesman) ? user.FullName : request.NewSalesman;

            var customer = await _customerRepository.GetByKeyAsync(request.CustomerId);
            if (customer == null || !customer.IsPublicPool)
            {
                return false;
            }

            customer.ClaimFromPublicPool(newSalesman);

            _dbContext.BeginTransaction();
            await _customerRepository.UpdateAsync(customer);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
