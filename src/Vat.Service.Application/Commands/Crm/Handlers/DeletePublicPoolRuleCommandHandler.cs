using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class DeletePublicPoolRuleCommandHandler : IRequestHandler<DeletePublicPoolRuleCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IPublicPoolRuleRepository _publicPoolRuleRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<DeletePublicPoolRuleCommandHandler> _logger;

        public DeletePublicPoolRuleCommandHandler(
            IDBContext dbContext,
            IPublicPoolRuleRepository publicPoolRuleRepository,
            ITypeAdapter typeAdapter,
            ILogger<DeletePublicPoolRuleCommandHandler> logger)
        {
            _dbContext = dbContext;
            _publicPoolRuleRepository = publicPoolRuleRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
        }

        public async Task<bool> Handle(DeletePublicPoolRuleCommand request, CancellationToken cancellationToken)
        {
            var rule = await _publicPoolRuleRepository.GetByKeyAsync(request.Id);
            if (rule == null)
            {
                return false;
            }

            rule.Delete();

            // _dbContext.BeginTransaction();
            await _publicPoolRuleRepository.UpdateAsync(rule);
            // await _dbContext.CommitAsync();
            return true;
        }
    }
}
