﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class AddProjectCommandHandler : IRequestHandler<AddProjectCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IProjectRepository _projectRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<AddProjectCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public AddProjectCommandHandler(IDBContext dbContext, IProjectRepository projectRepository, ITypeAdapter typeAdapter, ILogger<AddProjectCommandHandler> logger, IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _projectRepository = projectRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(AddProjectCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            Project customer = new Project(request.Name, request.Name, user.FullName, request.Country);
            _dbContext.BeginTransaction();
            await _projectRepository.AddAsync(customer);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
