﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.Common.Models;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class AddCustomerCommandHandler : IRequestHandler<AddCustomerCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly ICustomerRepository _customerRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<AddCustomerCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public AddCustomerCommandHandler(IDBContext dbContext, ICustomerRepository customerRepository, ITypeAdapter typeAdapter, ILogger<AddCustomerCommandHandler> logger, IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _customerRepository = customerRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(AddCustomerCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            List<CustomerContact> contacts = null;
            if (request.ContactList != null)
            {
                contacts = request.ContactList.Select(item => item.ToDomain()).ToList();
            }
            Customer customer = new Customer(request.Code, request.CnName, request.EnName, request.Contacts, request.Telephone, request.Email, request.Source, request.Type, user.FullName, request.AffiliatedCompany, request.ContactType, request.Salesman, request.Agent, request.Channel, "", contacts);
            _dbContext.BeginTransaction();
            await _customerRepository.AddAsync(customer);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
