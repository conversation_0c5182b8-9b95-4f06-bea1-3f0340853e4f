using BFE.Framework.Domain.Core;
using BFE.Framework.Domain.Core.Specification;
using BFE.Framework.Infrastructure.Crosscutting.EventBus;
using BFE.Framework.Infrastructure.Crosscutting.Json;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.IntegrationEvents.Events.NotificationCenter;
using Vat.Service.Application.ViewModels.Order;
using Vat.Service.Domain.AggregatesModel.OrderAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.TaxAgg;
using Vat.Service.Domain.AggregatesModel.TaxAgg.Specifications;
using Vat.Service.Domain.Constants.Status;
using Vat.Service.Domain.Repositories.Order;
using Vat.Service.Domain.Repositories.Tax;
using Vat.Service.Domain.AggregatesModel.OrderAgg;
using Vat.Service.Application.Commands.Crm;

namespace Vat.Service.Application.Commands.Order.Handlers
{
    public class ApproveSuccessToEmailCommandHandler : IRequestHandler<SendMailCommand, bool>
    {
        private readonly IIntegrationEventService _integrationEventService;

        private readonly IDBContext _dBContext;

        private readonly IJsonConverter _json;

        private readonly ILogger<ApproveSuccessToEmailCommandHandler> _logger;

        public ApproveSuccessToEmailCommandHandler(IIntegrationEventService integrationEventService, IDBContext dBContext, IJsonConverter json, ILogger<ApproveSuccessToEmailCommandHandler> logger)
        {
            _integrationEventService = integrationEventService;
            _dBContext = dBContext;
            _json = json;
            _logger = logger;
        }

        public async Task<bool> Handle(SendMailCommand request, CancellationToken cancellationToken)
        {
            var html = "审核成功的产品为：";
            for (int i = 0; i < request.Products.Count; i++)
            {
                var product = request.Products[i];
                html += product + "，";
            }
            html += "请登录系统查看";
            var type = request.Subject == "1" ? "收款" : "支出";
            var subject = $"{type}审核成功";
            var @event = new SendEmailToTenantImmediatelyByHtmlIntegrationEvent() { UniqueCode = Guid.NewGuid().ToString(), Content = html, BusinessModuleCode = "CRM", Subject = $"{subject}", TenantCode = "", To = request.Emails };

            _dBContext.BeginTransaction();

            await _integrationEventService.SaveIntegrationEvent(@event);

            await _dBContext.CommitAsync();
            await _integrationEventService.PublishAllAsync();

            return true;
        }
    }
}
