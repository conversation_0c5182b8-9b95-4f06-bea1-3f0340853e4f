﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class EditCrmAgentCommandHandler : IRequestHandler<EditCrmAgentCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly ICrmAgentRepository _CrmAgentRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<EditCrmAgentCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public EditCrmAgentCommandHandler(IDBContext dbContext, ICrmAgentRepository CrmAgentRepository, ITypeAdapter typeAdapter, ILogger<EditCrmAgentCommandHandler> logger, IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _CrmAgentRepository = CrmAgentRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(EditCrmAgentCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            var CrmAgent = await _CrmAgentRepository.GetByKeyAsync(request.Id);
            CrmAgent.Edit(request.Code, request.Name, request.Phone, request.Email, request.Leader, user.FullName, request.Contacts, request.Salesman, request.Role, request.ContactType);
            _dbContext.BeginTransaction();
            await _CrmAgentRepository.UpdateAsync(CrmAgent);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
