﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class AddCrmAgentCommandHandler : IRequestHandler<AddCrmAgentCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly ICrmAgentRepository _CrmAgentRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<AddCrmAgentCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public AddCrmAgentCommandHandler(IDBContext dbContext, ICrmAgentRepository CrmAgentRepository, ITypeAdapter typeAdapter, ILogger<AddCrmAgentCommandHandler> logger, IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _CrmAgentRepository = CrmAgentRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(AddCrmAgentCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            CrmAgent customer = new CrmAgent(request.Code, request.Name, request.Phone, request.Email, request.Leader, user.FullName, request.Contacts, request.Salesman, request.Role, request.ContactType);
            _dbContext.BeginTransaction();
            await _CrmAgentRepository.AddAsync(customer);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
