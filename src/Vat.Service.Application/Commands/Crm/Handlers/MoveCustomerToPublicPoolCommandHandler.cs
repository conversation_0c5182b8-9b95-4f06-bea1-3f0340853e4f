using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class MoveCustomerToPublicPoolCommandHandler : IRequestHandler<MoveCustomerToPublicPoolCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly ICustomerRepository _customerRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<MoveCustomerToPublicPoolCommandHandler> _logger;

        public MoveCustomerToPublicPoolCommandHandler(
            IDBContext dbContext,
            ICustomerRepository customerRepository,
            ITypeAdapter typeAdapter,
            ILogger<MoveCustomerToPublicPoolCommandHandler> logger)
        {
            _dbContext = dbContext;
            _customerRepository = customerRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
        }

        public async Task<bool> Handle(MoveCustomerToPublicPoolCommand request, CancellationToken cancellationToken)
        {
            var customer = await _customerRepository.GetByKeyAsync(request.CustomerId);
            if (customer == null || customer.IsPublicPool)
            {
                return false;
            }

            customer.MoveToPublicPool();

            _dbContext.BeginTransaction();
            await _customerRepository.UpdateAsync(customer);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
