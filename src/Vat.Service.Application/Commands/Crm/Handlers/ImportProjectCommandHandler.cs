﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using Magicodes.ExporterAndImporter.Excel;
using MediatR;
using Microsoft.Extensions.Logging;
using MWSCustomerService.Model;
using NPOI.Util;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Application.ViewModels.Crm;
using Vat.Service.Common.Model;
using Vat.Service.Common.Util.Excel;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications;
using Vat.Service.Domain.Repositories;
using Vat.Service.Infastructure.Exception;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class ImportProjectCommandHandler : IRequestHandler<ImportProjectCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IProjectRepository _projectRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<ImportProjectCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public ImportProjectCommandHandler(IDBContext dbContext, IProjectRepository projectRepository, ITypeAdapter typeAdapter, ILogger<ImportProjectCommandHandler> logger, IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _projectRepository = projectRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<bool> Handle(ImportProjectCommand request, CancellationToken cancellationToken)
        {
            var file = request.FormFile;
            IExcelImporter Importer = new ExcelImporter();
            var result = new Magicodes.ExporterAndImporter.Core.Models.ImportResult<ProjectImportModel>();
            using (Stream stream = file.OpenReadStream())
            {
                result = await Importer.Import<ProjectImportModel>(stream);
            }
            if (result.HasError)
            {
                string msg = "";
                if (result.TemplateErrors.Any())
                {
                    var errors = result.TemplateErrors.ToList();
                    msg = $"{string.Join(System.Environment.NewLine, errors.Select(e => $"{e.RequireColumnName}:{e.Message}"))}";
                }
                else if (result.RowErrors.Any())
                {
                    msg = $"{string.Join(System.Environment.NewLine, result.RowErrors.Take(10).Select(e => $"第{e.RowIndex}行:{string.Join(",", e.FieldErrors.Select(f => f.Value))}"))}";
                }
                throw new VatCommonException("ImportError", msg);
            }
            var user = _adminIdentityService.GetAdminUserIdentity();
            var projects = new List<Domain.AggregatesModel.CrmAgg.Project>();
            foreach (var item in result.Data)
            {
                bool exists = await _projectRepository.ExistsAsync(new MatchProjectByKeyWordSpecification(item.Name));
                if (exists)
                    continue;
                Project project = new Project(item.Name, item.Name, user.FullName, "");
                projects.Add(project);
            }
            _dbContext.BeginTransaction();
            await _projectRepository.AddAsync(projects);
            await _dbContext.CommitAsync();
            return true;
        }
    }
}
