﻿using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using Magicodes.ExporterAndImporter.Excel;
using MediatR;
using Microsoft.Extensions.Logging;
using MWSCustomerService.Model;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Application.Queries;
using Vat.Service.Application.ViewModels.Crm;
using Vat.Service.Common.Model;
using Vat.Service.Common.Util.Excel;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications;
using Vat.Service.Domain.Repositories;
using Vat.Service.Infastructure.Exception;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class ImportServiceAgreementCommandHandler : IRequestHandler<ImportServiceAgreementCommand, bool>
    {
        private readonly IDBContext _dbContext;
        private readonly IServiceAgreementRepository _serviceAgreementRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<ImportServiceAgreementCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;
        private readonly ICustomerRepository _customerRepository;
        private readonly IChannelRepository _channelRepository;
        private readonly IProjectRepository _projectRepository;
        private readonly ICrmAgentRepository _crmAgentRepository;
        private readonly ICrmQueries _crmQueries;

        public ImportServiceAgreementCommandHandler(IDBContext dbContext, IServiceAgreementRepository serviceAgreementRepository, ITypeAdapter typeAdapter, ILogger<ImportServiceAgreementCommandHandler> logger, IAdminIdentityService adminIdentityService, ICustomerRepository customerRepository, IChannelRepository channelRepository, IProjectRepository projectRepository, ICrmAgentRepository crmAgentRepository, ICrmQueries crmQueries)
        {
            _dbContext = dbContext;
            _serviceAgreementRepository = serviceAgreementRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
            _customerRepository = customerRepository;
            _channelRepository = channelRepository;
            _projectRepository = projectRepository;
            _crmAgentRepository = crmAgentRepository;
            _crmQueries = crmQueries;
        }

        public async Task<bool> Handle(ImportServiceAgreementCommand request, CancellationToken cancellationToken)
        {
            var file = request.FormFile;
            IExcelImporter Importer = new ExcelImporter();
            var result = new Magicodes.ExporterAndImporter.Core.Models.ImportResult<ServiceAgreementImportModel>();
            using (Stream stream = file.OpenReadStream())
            {
                result = await Importer.Import<ServiceAgreementImportModel>(stream);
            }
            if (result.HasError)
            {
                string msg = "";
                if (result.TemplateErrors.Any())
                {
                    var errors = result.TemplateErrors.ToList();
                    msg = $"{string.Join(System.Environment.NewLine, errors.Select(e => $"{e.RequireColumnName}:{e.Message}"))}";
                }
                else if (result.RowErrors.Any())
                {
                    msg = $"{string.Join(System.Environment.NewLine, result.RowErrors.Take(10).Select(e => $"第{e.RowIndex}行:{string.Join(",", e.FieldErrors.Select(f => f.Value))}"))}";
                }
                throw new VatCommonException("ImportError", msg);
            }
            var user = _adminIdentityService.GetAdminUserIdentity();
            var agents = await _crmQueries.GetAgentItems("");

            foreach (var item in result.Data)
            {
                //bool exists=await _serviceAgreementRepository.ExistsAsync(new MatchServiceAgreementByKeyWordSpecification(item.CompanyName));
                //if (exists)
                //    continue;
                try
                {
                    _dbContext.BeginTransaction();

                    string customerid = "";
                    var customer = await _customerRepository.GetAsync(new MatchCustomerByIsDeleteSpecification(false)
                        .And(new MatchCustomerByNameSpecification(item.CustomerCnName ?? item.CustomerEnName)));
                    if (customer == null)
                    {
                        customer = new Domain.AggregatesModel.CrmAgg.Customer(DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(99), item.CustomerCnName, item.CustomerEnName, item.Contacts ?? "", "", item.Email ?? "", item.Source ?? "", "", user.FullName, "", item.LegalName ?? "");
                        await _customerRepository.AddAsync(customer);
                    }
                    customerid = customer.Id;
                    string channelcode = "";
                    if (!item.ChannelName.IsNullOrBlank())
                    {
                        var channel = await _channelRepository.GetAsync(new MatchChannelByIsDeleteSpecification(false)
                            .And(new MatchChannelByKeyWordSpecification(item.ChannelName)));
                        if (channel == null)
                        {
                            channel = new Channel(DateTime.Now.ToString("yyyyMMddHHmmss"), item.ChannelName, "", "", "", "", 0, "", user.FullName, "", "", "");
                        }
                        channelcode = channel.Code;
                    }
                    string projectcode = "";
                    if (!item.ProjectName.IsNullOrBlank())
                    {
                        var project = await _projectRepository.GetAsync(new MatchProjectByIsDeleteSpecification(false)
                        .And(new MatchProjectByKeyWordSpecification(item.ProjectName)));
                        if (project == null)
                        {
                            project = new Project(item.ProjectName, item.ProjectName, user.FullName, "");
                            await _projectRepository.AddAsync(project);
                        }
                        projectcode = project.Code;
                    }
                    string crmagentcode = "";
                    if (!item.CrmAgent.IsNullOrBlank())
                    {
                        var crmagent = await _crmAgentRepository.GetAsync(new MatchCrmAgentByIsDeleteSpecification(false)
                        .And(new MatchCrmAgentByKeyWordSpecification(item.CrmAgent)));
                        if (crmagent == null)
                        {
                            crmagent = new CrmAgent(item.CrmAgent, item.CrmAgent, "", "", "", user.FullName, "", "", "", "");
                        }
                        crmagentcode = crmagent.Code;
                    }
                    var taxagent = agents.FirstOrDefault(a => a.Name == item.Agent);
                    string agreementcode = item.AgreementCode ?? (await _crmQueries.GetLastestServiceAgreementCode()).ToString();
                    ServiceAgreement agreement = new ServiceAgreement(item.Status, item.SignDate, customerid, agreementcode, channelcode, item.Country, projectcode, item.RegisterType, item.ContractStartDate, item.ContractEndDate, new Domain.Common.Models.Money(item.ContractAmount ?? 0, "CNY"), new Domain.Common.Models.Money(item.ReceiveAmount ?? 0, "CNY"), item.Salesman, item.CustomerServer, item.TaxManager, item.PartnerCode, new Domain.Common.Models.Money(item.CommissionAmount ?? 0, "CNY"), new Domain.Common.Models.Money(item.PaymentAmount ?? 0, "CNY"), taxagent?.Code, crmagentcode, item.DeclareMethod, item.Remark, "", "", "", user.FullName, item.DeclareStatus, item.AgentAdress, item.Collaborators, item.BackendAccountant, new Domain.Common.Models.Money(item.InvoicedAmount ?? 0, "CNY"), new Domain.Common.Models.Money(item.UnInvoicedAmount ?? 0, "CNY"), new Domain.Common.Models.Money(item.PaidAmount ?? 0, "CNY"), new Domain.Common.Models.Money(item.UnPaidAmount ?? 0, "CNY"), item.MarketSource);

                    await _serviceAgreementRepository.AddAsync(agreement);
                    await _dbContext.CommitAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"导入服务协议异常：{item.CustomerEnName} {item.Country} {item.RegisterType} {ex.Message}");
                }
            }
            return true;
        }
    }
}
