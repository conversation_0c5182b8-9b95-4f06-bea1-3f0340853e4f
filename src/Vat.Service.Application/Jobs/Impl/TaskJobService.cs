using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using Vat.Service.Application.Commands.ExchangeRate;
using Vat.Service.Application.Commands.IOSSOrder;
using Vat.Service.Application.Commands.NationalBusiness;
using Vat.Service.Application.Commands.Order;
using Vat.Service.Application.Commands.Tax;
using Vat.Service.Application.Commands.VatDeclarePending;
using Vat.Service.Application.Commands.VatDeclareSalesData;
using Vat.Service.Application.Commands.VatLog;
using Vat.Service.Application.Commands.VatNotification;
using Vat.Service.Application.Commands.VatRegister;
using Vat.Service.Application.Commands.VatRepair;
using Vat.Service.Application.Constants;
using Vat.Service.Application.Services;

namespace Vat.Service.Application.Jobs.Impl
{
    public class TaskJobService : ITaskJobServiceService
    {
        private readonly ILogger<TaskJobService> _logger = null;
        private readonly IMediator _mediator = null;
        private readonly PublicPoolService _publicPoolService = null;

        public TaskJobService(ILogger<TaskJobService> logger, IMediator mediator, PublicPoolService publicPoolService)
        {
            _logger = logger;
            _mediator = mediator;
            _publicPoolService = publicPoolService;
        }

        /// <summary>
        /// 统计所有需要发送提醒的客户
        /// </summary>
        /// <returns></returns>
        public async Task StaticticsTask()
        {
            var command = new VatStatisticsCommand();
            await _mediator.Send(command);

        }

        /// <summary>
        /// 英国VAT申报提醒
        /// </summary>
        /// <returns></returns>
        public async Task GBSendNotification()
        {
            _logger.LogInformation("Start Time:" + DateTime.UtcNow);
            Console.WriteLine("Start Time:" + DateTime.UtcNow);

            var command = new VatNotificationCommand(VatNotificationType.GBVatDeclareReminder, string.Empty);
            await _mediator.Send(command);

            _logger.LogInformation("End Time:" + DateTime.UtcNow);
            Console.WriteLine("End Time:" + DateTime.UtcNow);
        }

        /// <summary>
        /// 德国VAT申报提醒
        /// </summary>
        /// <returns></returns>
        public async Task DESendNotification()
        {
            _logger.LogInformation("Start Time:" + DateTime.UtcNow);
            Console.WriteLine("Start Time:" + DateTime.UtcNow);

            var command = new VatNotificationCommand(VatNotificationType.DEVatDeclareReminder, string.Empty);
            await _mediator.Send(command);

            _logger.LogInformation("End Time:" + DateTime.UtcNow);
            Console.WriteLine("End Time:" + DateTime.UtcNow);
        }

        /// <summary>
        /// 德国VAT季度申报提醒
        /// </summary>
        /// <returns></returns>
        public async Task DEQuarterlySendNotification()
        {
            _logger.LogInformation("Start Time:" + DateTime.UtcNow);
            Console.WriteLine("Start Time:" + DateTime.UtcNow);

            var command = new VatNotificationCommand(VatNotificationType.DEVatQuarterlyDeclareReminder, string.Empty);
            await _mediator.Send(command);

            _logger.LogInformation("End Time:" + DateTime.UtcNow);
            Console.WriteLine("End Time:" + DateTime.UtcNow);
        }

        /// <summary>
        /// 西班牙VAT申报提醒
        /// </summary>
        /// <returns></returns>
        public async Task ESSendNotification()
        {
            _logger.LogInformation("Start Time:" + DateTime.UtcNow);
            Console.WriteLine("Start Time:" + DateTime.UtcNow);

            var command = new VatNotificationCommand(VatNotificationType.ESVatDeclareReminder, string.Empty);
            await _mediator.Send(command);

            _logger.LogInformation("End Time:" + DateTime.UtcNow);
            Console.WriteLine("End Time:" + DateTime.UtcNow);
        }

        /// <summary>
        /// 意大利VAT季度申报提醒
        /// </summary>
        /// <returns></returns>
        public async Task ITSendNotification()
        {
            _logger.LogInformation("Start Time:" + DateTime.UtcNow);
            Console.WriteLine("Start Time:" + DateTime.UtcNow);

            var command = new VatNotificationCommand(VatNotificationType.ITVatQuarterlyDeclareReminder, string.Empty);
            await _mediator.Send(command);

            _logger.LogInformation("End Time:" + DateTime.UtcNow);
            Console.WriteLine("End Time:" + DateTime.UtcNow);
        }

        /// <summary>
        /// 校验VAT亚马逊销售数据报表申请状态
        /// </summary>
        /// <returns></returns>
        public async Task CheckAmazonVatSaleDatasReportRequest()
        {
            var command = new CheckAmazonSalesDataRequestStatusCommand();
            await _mediator.Send(command);

        }

        /// <summary>
        /// 下载VAT亚马逊销售数据报表申请状态
        /// </summary>
        /// <returns></returns>
        public async Task DownLoadAmazonVatSaleDatasReport()
        {
            var command = new DownLoadAmazonSalesDataReportCommand();
            await _mediator.Send(command);

        }

        /// <summary>
        /// 定时清理待申报数据
        /// </summary>
        /// <returns></returns>
        public async Task VatDeclarePendingDataCleaning()
        {
            var command = new VatDeclarePendingDataCleaningCommand();
            await _mediator.Send(command);

        }

        /// <summary>
        /// 更新待申报refno
        /// </summary>
        /// <returns></returns>
        public async Task VatDeclarePendingUpdateRefno()
        {
            var command = new VatDeclarePendingRepairCommand();
            await _mediator.Send(command);

        }

        /// <summary>
        /// 修复vat注册申报方式
        /// </summary>
        /// <returns></returns>
        public async Task VatRegisterDeclareMethodRepair()
        {
            var command = new VatRegisterDeclareMethodRepairCommand();
            await _mediator.Send(command);

        }

        public async Task VatRegisterRecentDeclarationCycleRepair()
        {
            var command = new VatRegisterRecentDeclarationCycleRepairCommand();
            await _mediator.Send(command);
        }

        /// <summary>
        /// 定时每月1号统计出本月可准备申报事项
        /// </summary>
        /// <returns></returns>
        public async Task VatPreDeclareRemind()
        {
            var command = new VatPreDeclareRemindCommand();
            await _mediator.Send(command);
        }

        /// <summary>
        /// 更新日志类型
        /// </summary>
        /// <returns></returns>
        public async Task VatLogBusinessTypeUpdate()
        {
            var command = new VatLogBusinessTypeUpdateCommand();
            await _mediator.Send(command);

        }

        /// <summary>
        /// vat起征日期修复
        /// </summary>
        /// <returns></returns>
        public async Task VatStartTimeRepair()
        {
            var command = new VatStartTimeRepairCommand();
            await _mediator.Send(command);
        }

        /// <summary>
        /// frs生效日期修复
        /// </summary>
        /// <returns></returns>
        public async Task FRSEffectedTimeRepair()
        {
            var command = new FRSEffectedTimeRepairCommand();
            await _mediator.Send(command);
        }

        /// <summary>
        /// 发送邮件通知客户活动内容
        /// </summary>
        /// <returns></returns>
        public async Task SendMailNotice()
        {
            SendMailNoticeCommand command = new SendMailNoticeCommand();
            var result = await _mediator.Send(command);
        }

        /// <summary>
        /// 定时生成待申报订单
        /// </summary>
        /// <returns></returns>
        public async Task ToDelcareOrderGenerate()
        {
            ToDeclareOrderGenerateCommand command = new ToDeclareOrderGenerateCommand();
            var result = await _mediator.Send(command);
        }
        /// <summary>
        /// 定时生成IOSS待申报订单
        /// </summary>
        /// <returns></returns>
        public async Task ToDelcareIOSSOrderGenerate()
        {
            ToDeclareIOSSOrderGenerateCommand command = new ToDeclareIOSSOrderGenerateCommand();
            var result = await _mediator.Send(command);
        }
        /// <summary>
        /// 发送待申报通知
        /// </summary>
        /// <returns></returns>
        public async Task ToDelcareNotify()
        {
            ToDeclareNotifyCommand command = new ToDeclareNotifyCommand();
            var result = await _mediator.Send(command);
        }
        /// <summary>
        /// 更新德国税率
        /// </summary>
        /// <returns></returns>
        public async Task UpdateDeTaxrate()
        {
            UpdateVatNationalTaxRateCommand command = new UpdateVatNationalTaxRateCommand();
            var result = await _mediator.Send(command);
        }

        /// <summary>
        /// 获取汇率到系统
        /// 适用于英国的算税汇率
        /// </summary>
        /// <returns></returns>
        public async Task GetPoundExchangeRateToSystem()
        {
            var command = new GetPoundExchangeRateToSystemCommand();
            var result = await _mediator.Send(command);
        }

        /// <summary>
        /// 获取汇率到系统
        /// 适用于德国、法国、意大利、西班牙、奥地利的算税汇率
        /// </summary>
        /// <returns></returns>
        public async Task GetEuroExchangeRateToSystem()
        {
            var command = new GetEuroExchangeRateToSystemCommand();
            var result = await _mediator.Send(command);
        }

        /// <summary>
        /// 获取汇率到系统
        /// 适用于波兰的算税汇率
        /// </summary>
        /// <returns></returns>
        public async Task GetPolandExchangeRateToSystem()
        {
            var command = new GetPolandExchangeRateToSystemCommand();
            var result = await _mediator.Send(command);
        }

        /// <summary>
        /// 按天拉取欧元汇率
        /// </summary>
        /// <returns></returns>
        public async Task GetEuroExchangeRateByDayToSystem()
        {
            var command = new GetEuroExchangeRateByDayToSystemCommand();
            await _mediator.Send(command);
        }

        public async Task GetCZKExchangeRateToSystem()
        {
            var command = new GetCZKExchangeRateToSystemCommand();
            await _mediator.Send(command);
        }
        /// <summary>
        /// 发送税号状态统计通知提醒后端
        /// </summary>
        /// <returns></returns>
        public async Task SendTaxStatisticNotify()
        {
            var command = new TaxStatisticNotifyCommand();
            var result = await _mediator.Send(command);
        }
        /// <summary>
        /// 发送申报订单统计通知提醒后端
        /// </summary>
        /// <returns></returns>
        public async Task SendTaxDeclaredNotify()
        {
            var command = new TaxDeclaredNotifyCommand();
            var result = await _mediator.Send(command);
        }
        /// <summary>
        /// 开始生成发票
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        public async Task GeneratePLInvoice(StartGeneratePLInoviceCommand command)
        {
            _logger.LogInformation($"{command.OrderId}开始生成发票:");
            var result = await _mediator.Send(command);
        }
        /// <summary>
        /// 开始生成跨境B2B订单文件
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        public async Task GenerateEUOrder(StartGenerateEUOrderCommand command)
        {
            _logger.LogInformation($"{command.OrderId}开始生成跨境B2B订单文件:");
            var result = await _mediator.Send(command);
        }

        public async Task GetCZDeclareFormToEmail()
        {
            var command = new GetCZDeclareFormToEmailCommand();
            await _mediator.Send(command);
        }
        public async Task GetITDeclareFormToEmail()
        {
            var command = new GetITDeclareFormToEmailCommand();
            await _mediator.Send(command);
        }
        #region 续费提醒
        /// <summary>
        /// 生成续费提醒记录
        /// </summary>
        /// <returns></returns>
        public async Task GenerateRenewNotify()
        {
            var command = new GenerateRenewNotifyCommand();
            var result = await _mediator.Send(command);
        }
        /// <summary>
        /// 发送通知提醒
        /// </summary>
        /// <returns></returns>
        public async Task SendRenewNotify()
        {
            var command = new SendRenewNotifyCommand();
            var result = await _mediator.Send(command);
        }


        #endregion

        #region 公海管理
        /// <summary>
        /// 自动归属公海
        /// </summary>
        /// <returns></returns>
        public async Task AutoMoveToPublicPool()
        {
            _logger.LogInformation("开始执行自动归属公海任务");
            await _publicPoolService.AutoMoveToPublicPool();
            _logger.LogInformation("自动归属公海任务执行完成");
        }
        #endregion
    }
}
