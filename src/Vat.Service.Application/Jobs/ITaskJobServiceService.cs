﻿using System.Threading.Tasks;

namespace Vat.Service.Application.Jobs
{
    public interface ITaskJobServiceService
    {
        Task GBSendNotification();
        Task StaticticsTask();
        Task DESendNotification();
        Task DEQuarterlySendNotification();
        Task ESSendNotification();
        Task ITSendNotification();
        Task CheckAmazonVatSaleDatasReportRequest();
        Task DownLoadAmazonVatSaleDatasReport();
        Task VatDeclarePendingDataCleaning();
        Task VatPreDeclareRemind();
        Task SendMailNotice();
        Task ToDelcareOrderGenerate();
        Task ToDelcareNotify();
        Task UpdateDeTaxrate();

        Task ToDelcareIOSSOrderGenerate();
        #region 修复数据
        /// <summary>
        /// 更新待申报refno
        /// </summary>
        /// <returns></returns>
        Task VatDeclarePendingUpdateRefno();
        /// <summary>
        /// vat起征日期修复
        /// </summary>
        /// <returns></returns>
        Task VatStartTimeRepair();
        /// <summary>
        /// frs生效日期修复
        /// </summary>
        /// <returns></returns>
        Task FRSEffectedTimeRepair();
        /// <summary>
        /// 更新日志类型
        /// </summary>
        /// <returns></returns>
        Task VatLogBusinessTypeUpdate();
        #endregion
        #region 抓取汇率更新到维易
        /// <summary>
        /// 适用于英国的算税汇率
        /// </summary>
        /// <returns></returns>
        Task GetPoundExchangeRateToSystem();

        /// <summary>
        /// 适用于德国、法国、意大利、西班牙、奥地利的算税汇率
        /// </summary>
        /// <returns></returns>
        Task GetEuroExchangeRateToSystem();

        /// <summary>
        /// 适用于波兰的算税汇率
        /// </summary>
        /// <returns></returns>
        Task GetPolandExchangeRateToSystem();


        Task GetEuroExchangeRateByDayToSystem();

        Task GetCZKExchangeRateToSystem();
        #endregion

        Task GetCZDeclareFormToEmail();
        Task GetITDeclareFormToEmail();


        #region 发送通知提醒
        Task SendTaxDeclaredNotify();
        Task SendTaxStatisticNotify();
        #endregion
        #region 续费提醒
        Task SendRenewNotify();
        Task GenerateRenewNotify();
        #endregion

        #region 公海管理
        /// <summary>
        /// 自动归属公海
        /// </summary>
        /// <returns></returns>
        Task AutoMoveToPublicPool();
        #endregion
    }
}
