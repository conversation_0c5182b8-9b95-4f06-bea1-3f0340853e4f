using AngleSharp.Html.Parser;
using BFE.Framework.Infrastructure.Authorization.Admin;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NPOI.OpenXmlFormats.Spreadsheet;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using Vat.Service.Application.Commands.Crm;
using Vat.Service.Application.Commands.ExchangeRate;
using Vat.Service.Application.Common.Model;
using Vat.Service.Application.DTO.Common;
using Vat.Service.Application.Queries;
using Vat.Service.Application.ViewModels.Crm;
using Vat.Service.Application.ViewModels.Crm.Request;

namespace Vat.Service.AdminApi.Controllers
{
    [Route("api/[controller]")]
    // [Authorize]
    [ApiController]
    public class CrmController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ICrmQueries _crmQueries;
        private readonly ILogger<CrmController> _logger;

        public CrmController(IMediator mediator, ICrmQueries crmQueries, ILogger<CrmController> logger)
        {
            _mediator = mediator;
            _crmQueries = crmQueries;
            _logger = logger;
        }

        #region crm old

        ///// <summary>
        ///// CRM客户分页
        ///// </summary>
        ///// <param name="request"></param>
        ///// <returns></returns>
        //[HttpPost("page")]
        //[ProducesResponseType(200, Type = typeof(PageResult<CrmCustomerModel>))]
        //public async Task<IActionResult> GetCrmCustomerList(CrmCustomerParam request)
        //{
        //    var result = await _crmQueries.GetCrmCustomerPageList(request);
        //    return Ok(result);
        //}
        ///// <summary>
        ///// CRM客户详情
        ///// </summary>
        ///// <param name="id"></param>
        ///// <returns></returns>
        //[HttpGet("{id}")]
        //[ProducesResponseType(200, Type = typeof(CrmCustomerModel))]
        //public async Task<IActionResult> GetCrmCustomerDetail(string id)
        //{
        //    var result = await _crmQueries.GetCrmCustomerDetail(id);
        //    return Ok(result);
        //}
        ///// <summary>
        ///// 添加Crm客户
        ///// </summary>
        //[HttpPost("")]
        //public async Task<IActionResult> AddCrmCustomer(AddCrmCustomerCommand comand)
        //{
        //    var result = await _mediator.Send(comand);
        //    return Ok(result);
        //}
        ///// <summary>
        ///// 编辑Crm客户
        ///// </summary>
        //[HttpPut("info")]
        //public async Task<IActionResult> EditCrmCustomer(EditCrmCustomerCommand comand)
        //{
        //    var result = await _mediator.Send(comand);
        //    return Ok(result);
        //}
        #endregion

        #region 客户管理
        /// <summary>
        /// 客户分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("customer/page")]
        [ProducesResponseType(200, Type = typeof(PageResult<CustomerModel>))]
        public async Task<IActionResult> GetCustomerList(CustomerParam request)
        {
            var result = await _crmQueries.GetCustomerPageList(request);
            return Ok(result);
        }
        /// <summary>
        /// 客户详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("customer/{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerModel))]
        public async Task<IActionResult> GetCustomerDetail(string id)
        {
            var result = await _crmQueries.GetCustomerDetail(id);
            return Ok(result);
        }
        /// <summary>
        /// 客户筛选项
        /// </summary>
        /// <returns></returns>
        [HttpGet("customer/items")]
        [ProducesResponseType(200, Type = typeof(CustomerModel))]
        public async Task<IActionResult> GetCustomerItems(string keywords)
        {
            var result = await _crmQueries.GetCustomerItems(keywords);
            return Ok(result);
        }
        /// <summary>
        /// 添加客户
        /// </summary>
        [HttpPost("customer")]
        public async Task<IActionResult> AddCustomer(AddCustomerCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 编辑客户
        /// </summary>
        [HttpPut("customer/info")]
        public async Task<IActionResult> EditCustomer(EditCustomerCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 删除客户
        /// </summary>
        [HttpDelete("customer")]
        public async Task<IActionResult> DeleteCustomer(DeleteCustomerCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 下载客户数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("customer/download")]
        public async Task<FileResult> DownloadSaleData(CustomerParam request)
        {
            var stream = await _crmQueries.ExportCustomer(request);
            var name = "客户信息_{0}.xlsx";

            string fileName = string.Format(name, DateTime.Now.ToString("yyyyMMdd"));
            var result = File(stream, "application/vnd.ms-excel; charset=utf-8", fileName);

            return result;
        }
        /// <summary>
        /// 导入客户
        /// </summary>
        [HttpPost("customer/import")]
        public async Task<IActionResult> ImportCustomer([FromForm] ImportCustomerCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        #endregion

        #region 渠道管理
        /// <summary>
        /// 渠道分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("channel/page")]
        [ProducesResponseType(200, Type = typeof(PageResult<ChannelModel>))]
        public async Task<IActionResult> GetChannelList(ChannelParam request)
        {
            var result = await _crmQueries.GetChannelPageList(request);
            return Ok(result);
        }
        /// <summary>
        /// 渠道详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("channel/{id}")]
        [ProducesResponseType(200, Type = typeof(ChannelModel))]
        public async Task<IActionResult> GetChannelDetail(string id)
        {
            var result = await _crmQueries.GetChannelDetail(id);
            return Ok(result);
        }
        /// <summary>
        /// 渠道筛选项
        /// </summary>
        /// <returns></returns>
        [HttpGet("channel/items")]
        [ProducesResponseType(200, Type = typeof(List<SelectItem>))]
        public async Task<IActionResult> GetChannelItems(string keywords)
        {
            var result = await _crmQueries.GetChannelItems(keywords);
            return Ok(result);
        }
        /// <summary>
        /// 添加渠道
        /// </summary>
        [HttpPost("channel")]
        public async Task<IActionResult> AddChannel(AddChannelCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 编辑渠道
        /// </summary>
        [HttpPut("channel/info")]
        public async Task<IActionResult> EditChannel(EditChannelCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 删除渠道
        /// </summary>
        [HttpDelete("channel")]
        public async Task<IActionResult> DeleteChannel(DeleteChannelCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 下载渠道数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("channel/download")]
        public async Task<FileResult> DownloadChannelSaleData(ChannelParam request)
        {
            var stream = await _crmQueries.ExportChannel(request);
            var name = "渠道信息_{0}.xlsx";

            string fileName = string.Format(name, DateTime.Now.ToString("yyyyMMdd"));
            var result = File(stream, "application/vnd.ms-excel; charset=utf-8", fileName);

            return result;
        }
        /// <summary>
        /// 导入渠道
        /// </summary>
        [HttpPost("channel/import")]
        public async Task<IActionResult> ImportChannel([FromForm] ImportChannelCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        #endregion

        #region 项目管理
        /// <summary>
        /// 项目分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("project/page")]
        [ProducesResponseType(200, Type = typeof(PageResult<ProjectModel>))]
        public async Task<IActionResult> GetProjectList(ProjectParam request)
        {
            var result = await _crmQueries.GetProjectPageList(request);
            return Ok(result);
        }
        /// <summary>
        /// 项目详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("project/{id}")]
        [ProducesResponseType(200, Type = typeof(ProjectModel))]
        public async Task<IActionResult> GetProjectDetail(string id)
        {
            var result = await _crmQueries.GetProjectDetail(id);
            return Ok(result);
        }
        /// <summary>
        /// 项目筛选项
        /// </summary>
        /// <returns></returns>
        [HttpGet("project/items")]
        [ProducesResponseType(200, Type = typeof(List<SelectItem>))]
        public async Task<IActionResult> GetProjectItems(string keywords)
        {
            var result = await _crmQueries.GetProjectItems(keywords);
            return Ok(result);
        }
        /// <summary>
        /// 添加项目
        /// </summary>
        [HttpPost("project")]
        public async Task<IActionResult> AddProject(AddProjectCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 编辑项目
        /// </summary>
        [HttpPut("project/info")]
        public async Task<IActionResult> EditProject(EditProjectCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 删除项目
        /// </summary>
        [HttpDelete("project")]
        public async Task<IActionResult> DeleteProject(DeleteProjectCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 下载项目数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("project/download")]
        public async Task<FileResult> DownloadProjectSaleData(ProjectParam request)
        {
            var stream = await _crmQueries.ExportProject(request);
            var name = "项目信息_{0}.xlsx";

            string fileName = string.Format(name, DateTime.Now.ToString("yyyyMMdd"));
            var result = File(stream, "application/vnd.ms-excel; charset=utf-8", fileName);

            return result;
        }
        /// <summary>
        /// 导入项目
        /// </summary>
        [HttpPost("project/import")]
        public async Task<IActionResult> ImportProject([FromForm] ImportProjectCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        #endregion

        #region 服务协议
        /// <summary>
        /// 服务协议分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("serviceagreement/page")]
        [ProducesResponseType(200, Type = typeof(PageResult<ServiceAgreementModel>))]
        public async Task<IActionResult> GetServiceAgreementList(ServiceAgreementParam request)
        {
            var result = await _crmQueries.GetServiceAgreementPageList(request);
            return Ok(result);
        }
        /// <summary>
        /// 服务协议详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("serviceagreement/{id}")]
        [ProducesResponseType(200, Type = typeof(ServiceAgreementModel))]
        public async Task<IActionResult> GetServiceAgreementDetail(string id)
        {
            var result = await _crmQueries.GetServiceAgreementDetail(id);
            return Ok(result);
        }
        /// <summary>
        /// 最新协议code
        /// </summary>
        /// <returns></returns>
        [HttpGet("serviceagreement/code")]
        [ProducesResponseType(200, Type = typeof(string))]
        public async Task<IActionResult> GetServiceAgreementDetail()
        {
            var result = await _crmQueries.GetLastestServiceAgreementCode();
            return Ok(result);
        }
        /// <summary>
        /// 添加服务协议
        /// </summary>
        [HttpPost("serviceagreement")]
        public async Task<IActionResult> AddServiceAgreement(AddServiceAgreementCommand comand)
        {
            var result = false;
            // 先保存服务协议
            var serviceAgreementId = await _mediator.Send(comand);
            if (!string.IsNullOrEmpty(serviceAgreementId))
            {
                // 再保存产品订单
                var addProductOrderCommand = new AddProductOrderCommand
                {
                    Status = "未开始",
                    ServiceAgreementId = serviceAgreementId,
                };
                result = await _mediator.Send(addProductOrderCommand);
            }
            return Ok(result);
        }
        /// <summary>
        /// 编辑服务协议
        /// </summary>
        [HttpPut("serviceagreement/info")]
        public async Task<IActionResult> EditServiceAgreement(EditServiceAgreementCommand comand)
        {
            // 更新服务协议
            var result = await _mediator.Send(comand);
            if (result)
            {
                // 再保存产品订单
                var addProductOrderCommand = new AddProductOrderCommand
                {
                    Status = "未开始",
                    ServiceAgreementId = comand.Id,
                };
                result = await _mediator.Send(addProductOrderCommand);
            }
            return Ok(result);
        }
        /// <summary>
        /// 更新服务协议状态
        /// </summary>
        [HttpPut("serviceagreement/{id}/status")]
        public async Task<IActionResult> UpdateServiceAgreementStatusCommand(string id, [FromBody] UpdateServiceAgreementStatusCommand comand)
        {
            comand.SetId(id);
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 删除服务协议
        /// </summary>
        [HttpDelete("serviceagreement")]
        public async Task<IActionResult> DeleteServiceAgreement(DeleteServiceAgreementCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 下载服务协议数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("serviceagreement/download")]
        public async Task<FileResult> DownloadAgreementSaleData(ServiceAgreementParam request)
        {
            var stream = await _crmQueries.ExportAgreement(request);
            var name = "服务协议信息_{0}.xlsx";

            string fileName = string.Format(name, DateTime.Now.ToString("yyyyMMdd"));
            var result = File(stream, "application/vnd.ms-excel; charset=utf-8", fileName);

            return result;
        }
        /// <summary>
        /// 导入服务协议
        /// </summary>
        [HttpPost("serviceagreement/import")]
        public async Task<IActionResult> ImportServiceAgreement([FromForm] ImportServiceAgreementCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        #endregion

        /// <summary>
        /// 税代筛选项
        /// </summary>
        /// <returns></returns>
        [HttpGet("agent/items")]
        [ProducesResponseType(200, Type = typeof(List<SelectItem>))]
        public async Task<IActionResult> GetAgentItems(string keywords)
        {
            var result = await _crmQueries.GetAgentItems(keywords);
            return Ok(result);
        }
        #region CRM代理管理
        /// <summary>
        /// crm代理分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("crmagenet/page")]
        [ProducesResponseType(200, Type = typeof(PageResult<CrmAgentModel>))]
        public async Task<IActionResult> GetCrmAgentList(CrmAgentParam request)
        {
            var result = await _crmQueries.GetCrmAgentPageList(request);
            return Ok(result);
        }
        /// <summary>
        /// crm代理详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("crmagent/{id}")]
        [ProducesResponseType(200, Type = typeof(CrmAgentModel))]
        public async Task<IActionResult> GetCrmAgentDetail(string id)
        {
            var result = await _crmQueries.GetCrmAgentDetail(id);
            return Ok(result);
        }
        /// <summary>
        /// crm代理筛选项
        /// </summary>
        /// <returns></returns>
        [HttpGet("crmagent/items")]
        [ProducesResponseType(200, Type = typeof(List<SelectItem>))]
        public async Task<IActionResult> GetCrmAgentItems(string keywords)
        {
            var result = await _crmQueries.GetCrmAgentItems(keywords);
            return Ok(result);
        }
        /// <summary>
        /// 添加CRM代理
        /// </summary>
        [HttpPost("crmagent")]
        public async Task<IActionResult> AddCrmAgent(AddCrmAgentCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 编辑crm代理
        /// </summary>
        [HttpPut("crmagent/info")]
        public async Task<IActionResult> EditCrmAgent(EditCrmAgentCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 删除crm代理
        /// </summary>
        [HttpDelete("crmagent")]
        public async Task<IActionResult> DeleteCrmAgent(DeleteCrmAgentCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 下载CRM代理数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("crmagent/download")]
        public async Task<FileResult> DownloadCrmAgentSaleData(CrmAgentParam request)
        {
            var stream = await _crmQueries.ExportCrmAgent(request);
            var name = "代理信息_{0}.xlsx";

            string fileName = string.Format(name, DateTime.Now.ToString("yyyyMMdd"));
            var result = File(stream, "application/vnd.ms-excel; charset=utf-8", fileName);

            return result;
        }
        /// <summary>
        /// 导入crm代理
        /// </summary>
        [HttpPost("crmagent/import")]
        public async Task<IActionResult> ImportCrmAgent([FromForm] ImportCrmAgentCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        #endregion

        #region 配置获取

        /// <summary>
        /// 跟进状态
        /// </summary>
        /// <returns></returns>
        [HttpGet("followstatus")]
        [ProducesResponseType(200, Type = typeof(List<string>))]
        public async Task<IActionResult> GetFallowStatus()
        {
            List<string> result = new List<string> { "已提交", "已下号", "注销", "暂停", "取消代理关系", "未跟进", "英国转代理改地址已寄出", "英国转代理地址已改", "英国转代理等待授权码", "英国转代理授权码已添加", "已完成", "转代理成功可申报", "处理跟进中" };
            return Ok(result);
        }
        /// <summary>
        /// 注册类型
        /// </summary>
        /// <returns></returns>
        [HttpGet("registertype")]
        [ProducesResponseType(200, Type = typeof(List<string>))]
        public async Task<IActionResult> GetRegisterType()
        {
            List<string> result = new List<string> { "新注册", "转代理", "续签", "注销" };
            return Ok(result);
        }
        /// <summary>
        /// 申报方式
        /// </summary>
        /// <returns></returns>
        [HttpGet("declaremethod")]
        [ProducesResponseType(200, Type = typeof(List<string>))]
        public async Task<IActionResult> GetDeclareMethod()
        {
            List<string> result = new List<string> { "月报", "季报", "年报", "不需要" };
            return Ok(result);
        }
        /// <summary>
        /// 客户类型
        /// </summary>
        /// <returns></returns>
        [HttpGet("customertype")]
        [ProducesResponseType(200, Type = typeof(List<string>))]
        public async Task<IActionResult> GetCustomerType()
        {
            List<string> result = new List<string> { "潜在客户", "有效客户", "成交客户", "优质客户", "无效客户", "滞留客户" };
            return Ok(result);
        }
        /// <summary>
        /// 客户来源
        /// </summary>
        /// <returns></returns>
        [HttpGet("customersource")]
        [ProducesResponseType(200, Type = typeof(List<string>))]
        public async Task<IActionResult> GetCustomerSource()
        {
            List<string> result = new List<string> { "线上流量（小红书）", "线上流量（视频号）", "线上流量（淘宝）", "线上流量（抖音）", "自拓客户", "代理", "渠道", " 线下活动", "公司资源", "分公司转介绍", "线上流量" };
            return Ok(result);
        }
        /// <summary>
        /// 所属公司
        /// </summary>
        /// <returns></returns>
        [HttpGet("affiliatedCompany")]
        [ProducesResponseType(200, Type = typeof(List<string>))]
        public async Task<IActionResult> GetAffiliatedCompany()
        {
            List<string> result = new List<string> { "深圳公司", "广州公司", "北京公司", "杭州公司", "山西公司" };
            return Ok(result);
        }
        #endregion

        #region 数据维护
        /// <summary>
        /// 迁移客户数据
        /// </summary>
        [HttpPost("customer/Migrate")]
        public async Task<IActionResult> MigrateCustomerData([FromBody] MigrateCustomerDataCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        #endregion

        #region 客户跟进记录
        /// <summary>
        /// 跟进记录分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("followup/page")]
        [ProducesResponseType(200, Type = typeof(PageResult<FollowupModel>))]
        public async Task<IActionResult> GetFollowupList(FollowupParam request)
        {
            var result = await _crmQueries.GetFollowupPageList(request);
            return Ok(result);
        }

        /// <summary>
        /// 添加跟进记录
        /// </summary>
        [HttpPost("followup")]
        public async Task<IActionResult> AddFollowup(AddFollowupCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 编辑跟进记录
        /// </summary>
        [HttpPut("followup/info")]
        public async Task<IActionResult> EditFollowup(EditFollowupCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 删除跟进记录
        /// </summary>
        [HttpDelete("followup")]
        public async Task<IActionResult> DeleteFollowup(DeleteFollowupCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        #endregion

        #region 服务协议对应的产品订单
        /// <summary>
        /// 产品订单分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("produceOrder/page")]
        [ProducesResponseType(200, Type = typeof(PageResult<ProductOrderModel>))]
        public async Task<IActionResult> GetProductOrderList(ProductOrderParam request)
        {
            var result = await _crmQueries.GetProductOrderPageList(request);
            return Ok(result);
        }
        /// <summary>
        /// 产品订单详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("productOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(ProductOrderModel))]
        public async Task<IActionResult> GetProductOrderDetail(string id)
        {
            var result = await _crmQueries.GetProductOrderDetail(id);
            return Ok(result);
        }
        /// <summary>
        /// 添加产品订单
        /// </summary>
        [HttpPost("productOrder")]
        public async Task<IActionResult> AddProductOrder(AddProductOrderCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 编辑产品订单
        /// </summary>
        [HttpPut("productOrder/info")]
        public async Task<IActionResult> EditProductOrder(EditProductOrderCommand comand)
        {
            var result = await _mediator.Send(comand);
            return Ok(result);
        }
        /// <summary>
        /// 删除产品订单
        /// </summary>
        [HttpDelete("productOrder")]
        public async Task<IActionResult> DeleteProductOrder(DeleteProductOrderCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        #endregion

        #region 产品订单交接记录
        /// <summary>
        /// 产品订单交接记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("productOrderHandover/all")]
        [ProducesResponseType(200, Type = typeof(List<ProductOrderHandoverModel>))]
        public async Task<IActionResult> GetProductOrderHandoverList(ProductOrderHandoverParam request)
        {
            var result = await _crmQueries.GetProductOrderHandoverList(request.ProductOrderId);
            return Ok(result);
        }
        /// <summary>
        /// 新增产品订单交接记录
        /// </summary>
        [HttpPost("productOrderHandover")]
        public async Task<IActionResult> AddProductOrderHandover(AddProductOrderHandoverCommand command)
        {
            var result = await _mediator.Send(command);
            // 查询产品订单状态，如何是未开始则变更为交接中
            var productOrderDetail = await _crmQueries.GetProductOrderDetail(command.ProductOrderId);
            if (productOrderDetail != null)
            {
                EditProductOrderCommand editProductOrderCommand;
                if (productOrderDetail.Status == "未开始")
                {
                    editProductOrderCommand = new EditProductOrderCommand()
                    {
                        Id = command.ProductOrderId,
                        Status = "交接中",
                        Remark = productOrderDetail.Remark,
                        CustomerServiceUser = command.CustomerServiceUser.Code,
                        BackendUser = command.BackendUser.Code
                    };
                }
                else
                {
                    // 每次新增产品订单都要重新赋值客服人员和后端人员
                    editProductOrderCommand = new EditProductOrderCommand()
                    {
                        Id = command.ProductOrderId,
                        Status = productOrderDetail.Status,
                        Remark = productOrderDetail.Remark,
                        CustomerServiceUser = command.CustomerServiceUser.Code,
                        BackendUser = command.BackendUser.Code
                    };
                }
                result = await _mediator.Send(editProductOrderCommand);
            }

            return Ok(result);
        }

        /// <summary>
        /// 修改产品订单交接记录
        /// </summary>
        [HttpPut("productOrderHandover/info")]
        public async Task<IActionResult> EditProductOrderHandover(EditProductOrderHandoverCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 删除产品订单交接记录
        /// </summary>
        [HttpDelete("productOrderHandover")]
        public async Task<IActionResult> DeleteProductOrderHandover(DeleteProductOrderHandoverCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        #endregion

        #region 产品订单交付记录
        /// <summary>
        /// 获取产品订单交付记录列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("productOrderDelivery/all")]
        [ProducesResponseType(200, Type = typeof(List<Application.Models.Crm.ProductOrderDeliveryModel>))]
        public async Task<IActionResult> GetProductOrderDeliveryList(ProductOrderDeliveryParam request)
        {
            var result = await _crmQueries.GetProductOrderDeliveryList(request.ProductOrderId);
            return Ok(result);
        }

        /// <summary>
        /// 添加产品订单交付记录
        /// </summary>
        [HttpPost("productOrderDelivery")]
        public async Task<IActionResult> AddProductOrderDelivery(AddProductOrderDeliveryCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 编辑产品订单交付记录
        /// </summary>
        [HttpPut("productOrderDelivery/info")]
        public async Task<IActionResult> EditProductOrderDelivery(EditProductOrderDeliveryCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 删除产品订单交付记录
        /// </summary>
        [HttpDelete("productOrderDelivery")]
        public async Task<IActionResult> DeleteProductOrderDelivery(DeleteProductOrderDeliveryCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        #endregion

        #region 收款
        /// <summary>
        /// 收款分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("payment/page")]
        [ProducesResponseType(200, Type = typeof(PageResult<PaymentModel>))]
        public async Task<IActionResult> GetPaymentList(PaymentParam request)
        {
            var result = await _crmQueries.GetPaymentPageList(request);
            return Ok(result);
        }
        /// <summary>
        /// 收款详情
        /// </summary>
        /// <returns></returns>
        [HttpGet("payment/{id}")]
        [ProducesResponseType(200, Type = typeof(PaymentModel))]
        public async Task<IActionResult> GetPaymentDetail(string id)
        {
            var result = await _crmQueries.GetPaymentDetail(id);
            return Ok(result);
        }
        /// <summary>
        /// 新增收款
        /// </summary>
        [HttpPost("payment")]
        public async Task<IActionResult> AddPayment(AddPaymentCommand command)
        {
            if (command.PaymentDetails == null || command.PaymentDetails.Count == 0)
            {
                return Ok("收款明细不能为空");
            }
            var approveList = await _crmQueries.GetApproverList();
            var approve = approveList.FirstOrDefault(item => item.ApproverType == 1);
            // 初始化审核状态
            command.Auditors = approve.Approvers.Select(item =>
            {
                var auditor = new AuditorInfo();
                auditor.Code = item.Code;
                auditor.Name = item.Name;
                auditor.Status = "待审核";
                return auditor;
            }).ToList();
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修改收款
        /// </summary>
        [HttpPut("payment/info")]
        public async Task<IActionResult> EditPayment(EditPaymentCommand command)
        {
            if (command.PaymentDetails == null || command.PaymentDetails.Count == 0)
            {
                return Ok("收款明细不能为空");
            }
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 删除收款
        /// </summary>
        [HttpDelete("payment")]
        public async Task<IActionResult> DeletePayment(DeletePaymentCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 收款审核
        /// </summary>
        [HttpPut("payment/audit")]
        public async Task<IActionResult> AuditPayment(AuditPaymentCommand command)
        {
            var result = await _mediator.Send(command);
            // 判断是否所有人都审核
            var payment = await _crmQueries.GetPaymentDetail(command.Id);
            if (payment.Status == "审核通过")
            {
                try
                {
                    //发送邮件
                    var productNames = payment.PaymentDetails.Select(item => item.Products).SelectMany(item => item).Select(item => item.ProductName).Distinct().ToList();
                    var approveList = await _crmQueries.GetApproverList();
                    var emails = approveList.FirstOrDefault(item => item.ApproverType == 1).CCs.Select(item => item.Code).ToList();
                    var sendMailCommand = new SendMailCommand()
                    {
                        Products = productNames,
                        Subject = "1",
                        Emails = emails
                    };

                    await _mediator.Send(sendMailCommand);
                }
                catch (Exception ex)
                {
                    _logger.LogError("发送收款审核邮件失败：" + ex.Message);
                }
            }
            return Ok(result);
        }
        #endregion

        #region  审批人
        /// <summary>
        /// 查询审批人
        /// </summary>
        [HttpPost("approver/all")]
        public async Task<IActionResult> GetAllApprover()
        {
            var result = await _crmQueries.GetApproverList();
            return Ok(result);
        }
        /// <summary>
        /// 添加审批人
        /// </summary>
        [HttpPost("approver")]
        public async Task<IActionResult> AddApprover(AddApproverCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修改审批人
        /// </summary>
        [HttpPut("approver/info")]
        public async Task<IActionResult> EditApprover(EditApproverCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        #endregion

        #region 支出
        /// <summary>
        /// 支出分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("expense/page")]
        [ProducesResponseType(200, Type = typeof(PageResult<ExpenseModel>))]
        public async Task<IActionResult> GetExpenseList(ExpenseParam request)
        {
            var result = await _crmQueries.GetExpensePageList(request);
            return Ok(result);
        }
        /// <summary>
        /// 支出详情
        /// </summary>
        /// <returns></returns>
        [HttpGet("expense/{id}")]
        [ProducesResponseType(200, Type = typeof(ExpenseModel))]
        public async Task<IActionResult> GetExpenseDetail(string id)
        {
            var result = await _crmQueries.GetExpenseDetail(id);
            return Ok(result);
        }
        /// <summary>
        /// 添加支出
        /// </summary>
        [HttpPost("expense")]
        public async Task<IActionResult> AddExpense(AddExpenseCommand command)
        {
            if (command.ExpenseDetails == null || command.ExpenseDetails.Count == 0)
            {
                return Ok("支出明细不能为空");
            }
            var hasProductOrderId = true;
            if (command.ExpenseDetails.Any(item => item.Products.Any(p => p.ProductId == null)))
            {
                hasProductOrderId = false;
            }
            if (!hasProductOrderId)
            {
                return Ok("产品订单Id必须有值");
            }
            // 判断是否有服务协议的收款未审核通过
            var agreementCodeList = command.ExpenseDetails.Select(item => item.AgreementCode).ToList();
            for (int i = 0; i < agreementCodeList.Count; i++)
            {
                var agreementCode = agreementCodeList[i];
                var paymentList = await _crmQueries.GetPaymentPageList(new PaymentParam()
                {
                    AgreementCode = agreementCode
                });
                if (paymentList.TotalRecords == 0)
                {
                    return BadRequest("有服务协议未填写收款，请先填写");
                }
                paymentList = await _crmQueries.GetPaymentPageList(new PaymentParam()
                {
                    AgreementCode = agreementCode,
                    Status = "待审核"
                });
                if (paymentList.TotalRecords > 0)
                {
                    return BadRequest("有服务协议收款未审核通过，请先审核通过");
                }
            }

            // 获取所有审核人
            var approveList = await _crmQueries.GetApproverList();
            var approve = approveList.FirstOrDefault(item => item.ApproverType == 2);
            // 初始化审核状态
            command.Auditors = approve.Approvers.Select(item =>
            {
                var auditor = new AuditorInfo();
                auditor.Code = item.Code;
                auditor.Name = item.Name;
                auditor.Status = "待审核";
                return auditor;
            }).ToList();
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修改支出
        /// </summary>
        [HttpPut("expense/info")]
        public async Task<IActionResult> EditExpense(EditExpenseCommand command)
        {
            if (command.ExpenseDetails == null || command.ExpenseDetails.Count == 0)
            {
                return Ok("支出明细不能为空");
            }
            var hasProductOrderId = true;
            if (command.ExpenseDetails.Any(item => item.Products.Any(p => p.ProductId == null)))
            {
                hasProductOrderId = false;
            }
            if (!hasProductOrderId)
            {
                return Ok("产品订单Id必须有值");
            }
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 删除支出
        /// </summary>
        [HttpDelete("expense")]
        public async Task<IActionResult> DeleteExpense(DeleteExpenseCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 支出审核
        /// </summary>
        [HttpPut("expense/audit")]
        public async Task<IActionResult> AuditExpense(AuditExpenseCommand command)
        {
            var result = await _mediator.Send(command);
            // 判断是否所有人都审核
            var expense = await _crmQueries.GetExpenseDetail(command.Id);
            if (expense.Status == "审核通过")
            {
                // 修改产品订单状态为“已完成”
                var productIds = expense.ExpenseDetails.Select(item => item.Products).SelectMany(item => item).Select(item => item.ProductId).Distinct().ToList();
                foreach (var item in productIds)
                {
                    var productOrder = await _crmQueries.GetProductOrderDetail(item);
                    if (productOrder.Status != "已完成")
                    {
                        var editProductOrderCommand = new EditProductOrderCommand()
                        {
                            Id = productOrder.Id,
                            Status = "已完成",
                            Remark = productOrder.Remark,
                        };
                        await _mediator.Send(editProductOrderCommand);
                    }
                }
                try
                {
                    //发送邮件
                    var productNames = expense.ExpenseDetails.Select(item => item.Products).SelectMany(item => item).Select(item => item.ProductName).Distinct().ToList();
                    var approveList = await _crmQueries.GetApproverList();
                    var emails = approveList.FirstOrDefault(item => item.ApproverType == 2).CCs.Select(item => item.Code).ToList();
                    var sendMailCommand = new SendMailCommand()
                    {
                        Products = productNames,
                        Subject = "2",
                        Emails = emails
                    };

                    await _mediator.Send(sendMailCommand);
                }
                catch (Exception ex)
                {
                    _logger.LogError("发送收款审核邮件失败：" + ex.Message);
                }
            }
            return Ok(result);
        }
        /// <summary>
        /// 发送邮件
        /// </summary>
        [HttpPost("sendMail")]
        public async Task<IActionResult> SendMail(SendMailCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        #endregion

        #region 公海管理

        /// <summary>
        /// 获取公海规则列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("publicPoolRule/all")]
        [ProducesResponseType(200, Type = typeof(List<PublicPoolRuleModel>))]
        public async Task<IActionResult> GetPublicPoolRulePageList()
        {
            var result = await _crmQueries.GetPublicPoolRulePageList();
            return Ok(result);
        }

        /// <summary>
        /// 获取公海规则详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("publicPoolRule/{id}")]
        [ProducesResponseType(200, Type = typeof(PublicPoolRuleModel))]
        public async Task<IActionResult> GetPublicPoolRuleDetail(string id)
        {
            var result = await _crmQueries.GetPublicPoolRuleDetail(id);
            return Ok(result);
        }

        /// <summary>
        /// 添加公海规则
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("publicPoolRule")]
        public async Task<IActionResult> AddPublicPoolRule([FromBody] AddPublicPoolRuleCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 编辑公海规则
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("publicPoolRule/info")]
        public async Task<IActionResult> EditPublicPoolRule([FromBody] EditPublicPoolRuleCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 删除公海规则
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpDelete("publicPoolRule")]
        public async Task<IActionResult> DeletePublicPoolRule([FromBody] DeletePublicPoolRuleCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 获取公海客户列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost("publicPoolCustomer/page")]
        public async Task<IActionResult> GetPublicPoolCustomerPageList([FromBody] CustomerParam param)
        {
            var result = await _crmQueries.GetPublicPoolCustomerPageList(param);
            return Ok(result);
        }

        /// <summary>
        /// 从公海领取客户
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("publicPool/claim")]
        public async Task<IActionResult> ClaimCustomerFromPublicPool([FromBody] ClaimCustomerFromPublicPoolCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 移动客户到公海
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("publicPool/move")]
        public async Task<IActionResult> MoveToPublicPool([FromBody] AutoMoveToPublicPoolCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        #endregion
        [HttpPost("PlnExchangeRate")]
        public async Task<IActionResult> PlnExchangeRate()
        {
            var command = new GetPolandExchangeRateToSystemCommand();
            await _mediator.Send(command);
            return Ok("ok");
        }

        [HttpPost("Test/web")]
        public async Task<IActionResult> TestWeb()
        {
            try
            {
                // 证书文件路径，根据实际情况修改
                string certFilePath = Path.Combine(Directory.GetCurrentDirectory(), "ca.cert");

                // 检查证书文件是否存在
                if (!System.IO.File.Exists(certFilePath))
                {
                    return BadRequest($"证书文件不存在: {certFilePath}");
                }

                // 读取证书文件
                X509Certificate2 certificate = new X509Certificate2(certFilePath);

                // 目标URL
                string targetUrl = "https://www.bundesfinanzministerium.de/Datenportal/Daten/offene-daten/steuern-zoelle/umsatzsteuer-umrechnungskurse/umsatzsteuer-umrechnungskurse.html";

                // 设置安全协议
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

                // 创建证书验证回调函数
                ServicePointManager.ServerCertificateValidationCallback = (sender, cert, chain, sslPolicyErrors) =>
                {
                    // 对于测试目的，可以选择信任所有证书
                    // 在生产环境中应该进行更严格的验证
                    if (sslPolicyErrors == SslPolicyErrors.None)
                        return true;

                    // 如果有SSL错误，可以在这里添加自定义验证逻辑
                    // 例如验证特定的证书指纹或颁发者
                    return true; // 测试环境下信任所有证书
                };

                using (var wc = new WebClient())
                {
                    // 设置编码
                    wc.Encoding = Encoding.UTF8;

                    // 设置请求头
                    wc.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
                    wc.Headers.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
                    wc.Headers.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                    // 注意：Connection 和 Keep-Alive 头不能通过 Headers.Add 设置
                    // wc.Headers.Add("Accept-Encoding", "gzip, deflate, br"); // WebClient会自动处理编码
                    wc.Headers.Add("Cache-Control", "no-cache");
                    wc.Headers.Add("Pragma", "no-cache");

                    // 下载网页内容
                    var html = await wc.DownloadStringTaskAsync(targetUrl);

                    // 解析HTML
                    var parser = new HtmlParser();
                    var document = parser.ParseDocument(html);

                    // 提取一些基本信息作为测试结果
                    var title = document.Title;
                    var bodyLength = document.Body?.TextContent?.Length ?? 0;

                    // 返回测试结果
                    var result = new
                    {
                        Success = true,
                        Message = "网页抓取成功",
                        CertificateUsed = certificate.Subject,
                        TargetUrl = targetUrl,
                        PageTitle = title,
                        ContentLength = bodyLength,
                        Timestamp = DateTime.Now
                    };

                    return Ok(result);
                }
            }
            catch (FileNotFoundException ex)
            {
                return BadRequest($"证书文件未找到: {ex.Message}");
            }
            catch (WebException ex)
            {
                return BadRequest($"网络请求失败: {ex.Message}");
            }
            catch (Exception ex)
            {
                return BadRequest($"发生错误: {ex.Message}");
            }
            finally
            {
                // 重置证书验证回调
                ServicePointManager.ServerCertificateValidationCallback = null;
            }
        }
    }
}
