﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Audit.WebApi;
using BFE.Framework.Infrastructure.Authorization.Admin;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Vat.Service.AdminApi.Extensions;
using Vat.Service.Application.Commands.Tax;
using Vat.Service.Application.Commands.Tax.Repair;
using Vat.Service.Application.Commands.VatLetterManagement;
using Vat.Service.Application.DTO.Common;
using Vat.Service.Application.Queries;
using Vat.Service.Application.ViewModels.Order;
using Vat.Service.Application.ViewModels.Tax;
using Vat.Service.Application.ViewModels.Tax.Request;

namespace Vat.Service.AdminApi.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class TaxController : ControllerBase
    {
        private readonly ITaxNumberQueries _taxNumberQueries;
        private readonly IMediator _mediator;

        public TaxController(ITaxNumberQueries taxNumberQueries, IMediator mediator)
        {
            _taxNumberQueries = taxNumberQueries;
            _mediator = mediator;
        }


        /// <summary>
        /// 分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("page")]
        [Permission(PermissionCodes.TaxNumber.Page)]
        [ProducesResponseType(200, Type = typeof(PageResult<TaxNumberListModel>))]
        public async Task<IActionResult> GetTaxNumberList(TaxNumberParam request)
        {
            var result = await _taxNumberQueries.GetTaxNumberInPage(request);
            return Ok(result);
        }

        /// <summary>
        /// 税号详情
        /// </summary>
        /// <param id="id"></param>
        /// <returns></returns>
        [HttpPost("{id}")]
        [Permission(PermissionCodes.TaxNumber.Detail)]
        [ProducesResponseType(200, Type = typeof(TaxNumberModel))]
        public async Task<IActionResult> GetTaxNumberDetail(string id)
        {
            var result = await _taxNumberQueries.GetTaxNumberById(id);
            return Ok(result);
        }
        /// <summary>
        /// 税号申报列表
        /// </summary>
        /// <param id="id"></param>
        /// <returns></returns>
        [HttpPost("{id}/declared")]
        [Permission(PermissionCodes.TaxNumber.Detail)]
        [ProducesResponseType(200, Type = typeof(PageResult<TaxDeclaredModel>))]
        public async Task<IActionResult> GetTaxDeclaredList(string id, TaxDeclaredParam param)
        {
            param.SetTaxId(id);
            var result = await _taxNumberQueries.GetTaxDeclaredList(param);
            return Ok(result);
        }
        /// <summary>
        /// 税号主体信息
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        [HttpGet("companys")]
        [Permission(PermissionCodes.TaxNumber.Page)]
        [ProducesResponseType(200, Type = typeof(List<CompanyInfoModel>))]
        public async Task<IActionResult> GetCompanysByName(string name)
        {
            var result = await _taxNumberQueries.GetCompanysByName(name);
            return Ok(result);
        }
        /// <summary>
        /// 模糊查询主体租户信息
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        [HttpGet("simplecompanys")]
        [Permission(PermissionCodes.TaxNumber.Page)]
        [ProducesResponseType(200, Type = typeof(List<CompanySimpleModel>))]
        public async Task<IActionResult> GetSimpleCompanysByName(string name, bool isContainTax)
        {
            var result = await _taxNumberQueries.GetSimpleCompanysByName(name, isContainTax);
            return Ok(result);
        }
        /// <summary>
        /// 根据税号检索租户
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        [HttpGet("simpletaxs")]
        [Permission(PermissionCodes.TaxNumber.Page)]
        [ProducesResponseType(200, Type = typeof(List<CompanySimpleModel>))]
        public async Task<IActionResult> GetSimpleTaxList(string taxno)
        {
            var result = await _taxNumberQueries.GetSimpleTaxList(taxno);
            return Ok(result);
        }
        /// <summary>
        /// 税号主体信息
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        [HttpPost("companynames")]
        [ProducesResponseType(200, Type = typeof(List<CompanyNameModel>))]
        public async Task<IActionResult> GetCompanysByName(CompanyQueryParam param)
        {
            var result = await _taxNumberQueries.GetCompanyNames(param);
            return Ok(result);
        }
        #region 税号操作
        /// <summary>
        /// 解除授权
        /// </summary>
        [HttpPut("{id}/transferout")]
        [Permission(PermissionCodes.TaxNumber.Revoke)]
        public async Task<IActionResult> TransferOut(string id, TransferOutCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 申请注销
        /// </summary>
        [HttpPut("{id}/logout")]
        [Permission(PermissionCodes.TaxNumber.ApplyLogout)]
        public async Task<IActionResult> ApplyLogout(string id, ApplyLogoutCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 注销结果
        /// </summary>
        [HttpPut("{id}/logoutResult")]
        [Permission(PermissionCodes.TaxNumber.UploadLogoutResult)]
        public async Task<IActionResult> UpdateLogoutResult(string id, UpdateLogoutResultCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 申请启用
        /// </summary>
        [HttpPut("{id}/active")]
        [Permission(PermissionCodes.TaxNumber.ApplyActive)]
        public async Task<IActionResult> ApplyActive(string id, ApplyActiveCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 启用结果
        /// </summary>
        [HttpPut("{id}/activeresult")]
        [Permission(PermissionCodes.TaxNumber.UploadActiveResult)]
        public async Task<IActionResult> UpdateActiveResult(string id, UpdateActiveResultCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 申请停用
        /// </summary>
        [HttpPut("{id}/stop")]
        [Permission(PermissionCodes.TaxNumber.ApplyStop)]
        public async Task<IActionResult> ApplyStop(string id, ApplyStopCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 停用结果
        /// </summary>
        [HttpPut("{id}/stopresult")]
        [Permission(PermissionCodes.TaxNumber.UploadStopResult)]
        public async Task<IActionResult> UpdateStopResult(string id, UpdateStopResultCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 查询未完成的待申报
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}/unfinishedDeclare")]
        [Permission(PermissionCodes.DeclaredOrder.Page)]
        [ProducesResponseType(200, Type = typeof(List<UnFinishedDeclare>))]
        public async Task<IActionResult> GetUnFinishedDeclared(string id)
        {
            var result = await _taxNumberQueries.GetUnFinishedDeclared(id);
            return Ok(result);
        }
        #endregion

        /// <summary>
        /// 修改税号证书
        /// </summary>
        [HttpPut("{id}/vatproof")]
        [Permission(PermissionCodes.TaxNumber.ModifyVatProof)]
        public async Task<IActionResult> UploadVatProof(string id, ModifyVatProofCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);

            return Ok(result);
        }
        /// <summary>
        /// 修改税务证书
        /// </summary>
        [HttpPut("{id}/taxproof")]
        [Permission(PermissionCodes.TaxNumber.ModifyVatProof)]
        public async Task<IActionResult> ModifyTaxProof(string id, ModifyTaxProofCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);

            return Ok(result);
        }
        /// <summary>
        /// 修改企业税号
        /// </summary>
        [HttpPut("{id}/refno")]
        [Permission(PermissionCodes.TaxNumber.ModifyRefno)]
        public async Task<IActionResult> ModifyRefNo(string id, ModifyRefNoCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);

            return Ok(result);
        }
        /// <summary>
        /// 修改SIRET
        /// </summary>
        [HttpPut("{id}/siret")]
        [Permission(PermissionCodes.TaxNumber.ModifyVatProof)]
        public async Task<IActionResult> ModifySIRET(string id, ModifySIRETCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);

            return Ok(result);
        }
        /// <summary>
        /// 修改IOSS code
        /// </summary>
        [HttpPut("{id}/ioss")]
        [Permission(PermissionCodes.TaxNumber.ModifyVatProof)]
        public async Task<IActionResult> ModifyIOSSCode(string id, ModifyIOSSCodeCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);

            return Ok(result);
        }
        /// <summary>
        /// 更改申报方式
        /// </summary>
        [HttpPut("{id}/declaremethod")]
        [Permission(PermissionCodes.TaxNumber.ModifyDeclareType)]
        public async Task<IActionResult> UploadVatProof(string id, ModifyDeclareMethodCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);

            return Ok(result);
        }
        /// <summary>
        /// 更改首次申报周期
        /// </summary>
        [HttpPut("{id}/declaredcycle")]
        [Permission(PermissionCodes.TaxNumber.ModifyDeclareType)]
        public async Task<IActionResult> ModifyDeclardCycle(string id, ModifyDeclaredCycleCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);

            return Ok(result);
        }
        /// <summary>
        /// 更新最近一次申报日期
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("declareDate")]
        public async Task<IActionResult> DeclareMonthDataMigration(UpdateRecentDeclareDateCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新frs文件
        /// </summary>
        /// <param name="id"></param>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{id}/frs")]
        [Permission(PermissionCodes.TaxNumber.ModifyFrs)]
        public async Task<IActionResult> UpdateFrsFile(string id, UpdateFrsFileCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetId(id);
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 导出excel
        /// </summary>
        [HttpPost("export")]
        [Permission(PermissionCodes.TaxNumber.Export)]
        [ProducesResponseType(200, Type = typeof(FileResult))]
        public async Task<FileResult> ExportRegisterOrders([FromBody] TaxNumberParam param)
        {
            var stream = await _taxNumberQueries.ExportTaxNumbers(param);

            string fileName = string.Format("taxNumber_{0}.xlsx", DateTime.Now.ToString("yyyyMMddHHss"));
            var result = File(stream, "application/vnd.ms-excel", fileName);

            return result;
        }
        /// <summary>
        /// 导出税代表单模板
        /// </summary>
        [HttpPost("export/template")]
        [ProducesResponseType(200, Type = typeof(FileResult))]
        public async Task<FileResult> ExportTaxAgentTemplate([FromBody] ExportTaxAgentTemplateParam param)
        {
            var stream = await _taxNumberQueries.ExportTaxAgentTemplate(param);

            string fileName = string.Format("template_{0}.xlsx", DateTime.Now.ToString("yyyyMMddHHss"));
            var result = File(stream, "application/vnd.ms-excel", fileName);

            return result;
        }

        /// <summary>
        /// 工作看板统计
        /// </summary>
        [HttpGet("dashboard/statistic")]
        [Permission(PermissionCodes.TaxNumber.Page)]
        [ProducesResponseType(200, Type = typeof(DashboardStatisticModel))]
        public async Task<IActionResult> DashboardStatistic()
        {
            var result = await _taxNumberQueries.DashboardStatistic();
            return Ok(result);
        }

        /// <summary>
        /// 限制申报税率
        /// </summary>
        [HttpPut("frs/limit")]
        [ProducesResponseType(200, Type = typeof(string))]
        public async Task<IActionResult> SetFRSLimit([FromBody] FRSLimitCommand param)
        {
            var ip = HttpContext.GetClientUserIp();
            param.SetIp(ip);
            var result = await _mediator.Send(param);
            return Ok(result);
        }
        /// <summary>
        /// 更新税代管理税号Id
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{taxid}/agentid")]
        [Permission(PermissionCodes.TaxNumber.ModifyAgentCode)]
        public async Task<IActionResult> UpdateAgentId(string taxid, UpdateTaxAgentIdCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetTaxId(taxid);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更改客户管理code
        /// </summary>
        /// <param name="taxid"></param>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{taxid}/ManagerCode")]
        [Permission(PermissionCodes.TaxNumber.ModifyAgentCode)]
        public async Task<IActionResult> UpdateManagerCode(string taxid, UpdateTaxManagerCodeCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.TaxNumberId = taxid;
            command.Ip = ip;
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新纳税银行帐号
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{taxid}/bankaccount")]
        [Permission(PermissionCodes.TaxNumber.ModifyAgentCode)]
        public async Task<IActionResult> UpdatePayTaxBankAccount(string taxid, UpdatePayTaxBankAccountCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetTaxId(taxid);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新受理中文件
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{taxid}/acceptingfiles")]
        public async Task<IActionResult> UpdateAgentId(string taxid, ModifyAcceptingFileCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetTaxId(taxid);
            command.SetIpAdd(ip);
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 变更税号主体
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{taxid}/company")]
        [Permission(PermissionCodes.TaxNumber.ModifyVatTaxNumberCompany)]
        public async Task<IActionResult> ChangeTaxCompany(string taxid, ChangeTaxCompanyCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetId(taxid);
            command.SetIpAdd(ip);
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 按用户分组统计产品订单数据
        /// </summary>
        /// <param name="param">统计查询参数</param>
        /// <returns>按用户分组的统计数据</returns>
        [HttpPost("productorder/statistics")]
        // [Permission(PermissionCodes.TaxNumber.Page)]
        [ProducesResponseType(200, Type = typeof(List<ProductOrderStatisticsModel>))]
        public async Task<IActionResult> GetProductOrderStatistics([FromBody] ProductOrderStatisticsParam param)
        {
            var result = await _taxNumberQueries.GetProductOrderStatistics(param);
            return Ok(result);
        }

        /// <summary>
        /// 税号服务续费
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{taxid}/renew")]
        //[Permission(PermissionCodes.TaxNumber.ModifyVatTaxNumberCompany)]
        public async Task<IActionResult> RenewTax(string taxid, RenewTaxCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetId(taxid);
            command.SetIpAdd(ip);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet("{taxid}/paymentlist")]
        //[Permission(PermissionCodes.TaxNumber.Page)]
        [ProducesResponseType(200, Type = typeof(List<PaymentModel>))]
        public async Task<IActionResult> GetPaymentList(string taxid)
        {
            var result = await _taxNumberQueries.GetPaymentList(taxid);
            return Ok(result);
        }
        /// <summary>
        /// 更换税代
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("agent")]
        //[Permission(PermissionCodes.TaxNumber.ModifyAlterTaxAgent)]
        public async Task<IActionResult> AlterTaxAgentCommand(AlterTaxAgentCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修改已申报次数
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{taxid}/declaredamount")]
        //[Permission(PermissionCodes.TaxNumber.ModifyAlterTaxAgent)]
        public async Task<IActionResult> ModifyDeclaredAmount(string taxid, ModifyDeclaredAmountCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetTaxId(taxid);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修改最近一次购买服务价格
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{taxid}/recentpurchaseprice")]
        //[Permission(PermissionCodes.TaxNumber.ModifyAlterTaxAgent)]
        public async Task<IActionResult> ModifyRecentPurchasePrice(string taxid, ModifyRecentPurchasePriceCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetTaxId(taxid);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 生成待申报数据
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("todeclare")]
        public async Task<IActionResult> ToDeclareOrderGenerate(Application.Commands.Order.ToDeclareOrderGenerateCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 上传静态文件模板
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("statictemplate")]
        public async Task<IActionResult> UploadTemplate([FromForm] UploadTemplateCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 删除static文件夹下的临时文件
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("remove/tempfile")]
        public async Task<IActionResult> DeleteTempFile([FromForm] DeleteTempFileCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 线下税号导入检查
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("offlineTax/validate")]
        public async Task<IActionResult> ValidateData([FromForm] Application.Commands.OfflineData.ValidateDataCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 线下税号导入
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("offlineTax/import")]
        [AuditIgnore]
        public async Task<IActionResult> ImportOfflineData([FromForm] Application.Commands.OfflineData.ImportOfflineDataCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// IOSS线下税号导入
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("offlineTax/import/ioss")]
        [AuditIgnore]
        public async Task<IActionResult> ImportOfflineData([FromForm] Application.Commands.OfflineData.ImportIOSSOfflineDataCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        #region 续费提醒
        /// <summary>
        /// 续费提醒分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("renew/page")]
        [Permission(PermissionCodes.Renew.View)]
        [ProducesResponseType(200, Type = typeof(PageResult<TaxRenewListModel>))]
        public async Task<IActionResult> GetTaxRenewList(TaxRenewParam request)
        {
            var result = await _taxNumberQueries.GetRenewInPage(request);
            return Ok(result);
        }
        /// <summary>
        /// 修改最近一次购买服务周期
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("{taxid}/servicecycle")]
        public async Task<IActionResult> ModifyServiceCycle(string taxid, ModifyServiceCycleCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetTaxId(taxid);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 添加当前服务周期
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("{taxid}/servicecycle")]
        public async Task<IActionResult> AddCurrentServiceCycle(string taxid, AddCurrentServiceCycleCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);
            command.SetTaxId(taxid);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        #endregion
        #region 修复数据

        /// <summary>
        /// 更新历史数据税号服务时长
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("repair/servicedate")]
        [AuditIgnore]
        public async Task<IActionResult> UpdateHistoryTaxServiceDate([FromForm] UpdateHistoryTaxServiceDateCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 清理国家业务配置数据
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("nationbusinessclear")]
        public async Task<IActionResult> NationBusinessClear(DataClearCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 清理税号数据
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("taxnumberclear")]
        public async Task<IActionResult> TaxNumberClear(TaxDataClearCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 迁移数据
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("datamigration")]
        public async Task<IActionResult> DataMigration(DataMigrationCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 迁移申报月份数据
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("repair/declareMonthData")]
        public async Task<IActionResult> DeclareMonthDataMigration(TaxDeclareMonthMigrationCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        ///// <summary>
        ///// 批量更新注册主体名称
        ///// </summary>
        ///// <param name="formFile"></param>
        ///// <returns></returns>
        //[HttpPut("registermain/cnname")]
        //public async Task<IActionResult> BatchUpdateCompanyName(IFormFile formFile)
        //{
        //    BatchUpdateCompanyNameCommand command = new BatchUpdateCompanyNameCommand();
        //    command.FormFile = formFile;
        //    var result = await _mediator.Send(command);
        //    return Ok(result);
        //}


        /// <summary>
        /// 批量更新英文名称
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/enname")]
        public async Task<IActionResult> RepairCompanyEnName(RepairCompanyEnNameCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 批量税号中文名称
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/cnname")]
        public async Task<IActionResult> RepairTaxNumberCnName(RepairTaxNumberCnNameCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 批量更新税率
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/taxrate1")]
        public async Task<IActionResult> BatchUpdateTaxRate(RepairTaxRateCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复eori code
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/eori")]
        public async Task<IActionResult> RepairEORI(RepairEoriCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 修复订单，税号，申报记录的vatno，refno
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/taxno")]
        public async Task<IActionResult> RepairTaxNo(RepairTaxNoCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复解绑后又转税代的税号
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/unbound")]
        public async Task<IActionResult> RepairTaxNo(RepairTaxUnboundDataCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复申报税代名称
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/agentname")]
        public async Task<IActionResult> RepairTaxAgentName(RepairTaxAgentCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 删除税号
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTaxNumber(string id, DeleteTaxNumberCommand command)
        {
            command.TaxId = id;
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复税号申报对象
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("declaredObject")]
        public async Task<IActionResult> DeleteTaxNumber(RepairDeclaredObjectCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        ///// <summary>
        ///// 更新创建订单的业务经理
        ///// </summary>
        ///// <param name="command"></param>
        ///// <returns></returns>
        //[HttpPut("clientmanager")]
        //public async Task<IActionResult> RepairOriClientManager(RepairOriClientManagerCommand command)
        //{
        //    var result = await _mediator.Send(command);
        //    return Ok(result);
        //}

        /// <summary>
        /// 修复申报方式
        /// </summary>
        [HttpPut("repair/declaremethod")]
        public async Task<IActionResult> RepairDeclareMethod(RepairDeclareMethodCommand command)
        {
            var result = await _mediator.Send(command);

            return Ok(result);
        }
        /// <summary>
        /// 修复最新业务经理数据
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/clientmanager")]
        public async Task<IActionResult> RepairClientManager(RepairClientManagerCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复税代管理id
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/taxagentMangerId")]
        public async Task<IActionResult> RepairTaxAgentMangerId(RepairTaxAgentMangerIdCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 清理租户数据
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("clear/tenantdata")]
        public async Task<IActionResult> ClearTenantData(ClearTenantDataCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复frs限制
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/frslimit")]
        public async Task<IActionResult> RepairRepairFrsLimit(RepairFrsLimitCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 迁移c79文件
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        //[HttpPut("repair/lettermigration")]
        //public async Task<IActionResult> LetterMigration(VatLetterMigrationCommand command)
        //{
        //    var result = await _mediator.Send(command);
        //    return Ok(result);
        //}
        /// <summary>
        /// 删除测试的税号,订单数据
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("delete/invalidtax2")]
        public async Task<IActionResult> ClearInvalidTax(ClearInvalidTaxCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更正空白的税代名称
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/blankagent")]
        public async Task<IActionResult> RepairErrorAgent(RepairErrorAgentCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 更新税号中的税代id
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/agentid")]
        public async Task<IActionResult> RepairTaxAgentId(RepairTaxAgentIdCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新税号最近一次申报周期
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/recentcycle")]
        public async Task<IActionResult> RepairRecentCycle(RepairRecentCycleCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 恢复税号
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/recover")]
        public async Task<IActionResult> RecoverTax(RecoverTaxCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新税号通知邮箱
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/notifyemail")]
        public async Task<IActionResult> RepairTaxNotifyEmail(RepairTaxNotifyEmailCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复不规范税号
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/unspecTaxno")]
        public async Task<IActionResult> SpecificaTaxNo(SpecificaTaxNoCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复税号状态
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/status")]
        public async Task<IActionResult> RepairTaxStatus(RepairTaxStatusCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 修复线下导入税号的最近一次申报周期
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/offlineLastestCycle")]
        public async Task<IActionResult> RepairOfflineLastestCycle(RepairOfflineLastestCycleCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 检查重复的主体名称，并修复
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/companyinfo")]
        public async Task<IActionResult> RepairCompanyRepeat(RepairCompanyRepeatCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新税号线下管理code
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/managercode1")]
        public async Task<IActionResult> RepairManagerCode(RepairManagerCodeCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新税号税代管理id
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/agentmanagerid")]
        public async Task<IActionResult> RepairBatchManagerCode(RepairBatchManagerCodeCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 更新客户线下编码和税代管理税号ID通过Excel
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPut("repair/UploadManagerCodeAndAgentManagerIdByFile")]
        public async Task<FileResult> UploadManagerCodeAndAgentManagerIdByFile(IFormFile file)
        {
            var fs = file.OpenReadStream();
            var command = new UploadManagerCodeAndAgentManagerIdByFileCommand() { FileStream = fs };
            var streamReslut = await _mediator.Send(command);
            var result = File(streamReslut, "application/vnd.ms-excel", "Excel的税号在系统不存在");
            return result;
        }
        /// <summary>
        /// 修复主体信息
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/errorcompanyinfo")]
        public async Task<IActionResult> RepairCompanyInfo(RepairCompanyInfoCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [HttpPut("repair/ExportInfo")]
        public async Task<FileResult> Export()
        {
            var stream = await _taxNumberQueries.ExportTemporaryInfo();
            string fileName = string.Format("{0}.xlsx", DateTime.Now.ToString("yyyyMMddHHss"));
            var result = File(stream, "application/vnd.ms-excel", fileName);
            return result;
        }
        /// <summary>
        /// 修复税号已申报次数
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/declaredamount")]
        public async Task<IActionResult> InitTaxDeclaredAmount(InitTaxDeclaredAmountCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 删除无效的主体
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/company/clear")]
        public async Task<IActionResult> DeleteInvalidCompany(DeleteInvalidCompanyCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复申报订单税代管理id
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/declaredorder/agentmanagerid")]
        public async Task<IActionResult> RepairDeclareOrderAgentMangerId(RepairDeclareOrderAgentMangerIdCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新税号服务时长
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("repair/servicecycle1")]
        [AuditIgnore]
        public async Task<FileResult> ImportOfflineData([FromForm] RepairTaxServiceDateCommand command)
        {
            var stream = await _mediator.Send(command);
            var result = File(stream, "application/ms-excel; charset=utf-8", "更新结果.xlsx");
            return result;
        }
        /// <summary>
        /// 查找税号是否存在
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/checktaxno")]
        public async Task<IActionResult> CheckTaxNo(CheckTaxNoCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 清除异常的服务周期
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/serviceitem")]
        public async Task<IActionResult> ClearInvalidTaxServiceItem(ClearInvalidTaxServiceItemCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        #region IOSS
        /// <summary>
        /// 更新IOSS税号首次申报周期
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/ioss/firstcycle")]
        public async Task<IActionResult> RepairIOSSFirstCycle(RepairIOSSFirstCycleCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新IOSS主体英文名称
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/ioss/enname")]
        public async Task<IActionResult> RepairIOSSCompanyEnName(RepairIOSSCompanyEnNameCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新IOSS税代
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/ioss/agent")]
        public async Task<IActionResult> RepairIOSSAgent(RepairIOSSAgentCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        #endregion
        #endregion

    }
}