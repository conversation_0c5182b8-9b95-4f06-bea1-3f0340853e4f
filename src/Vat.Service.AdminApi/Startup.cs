using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Audit.WebApi;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using AutoMapper;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.EventBus;
using BFE.Framework.Infrastructure.Crosscutting.EventBus.RabbitMQ;
using BFE.Framework.Infrastructure.Data.MongoDB;
using DinkToPdf;
using DinkToPdf.Contracts;
using Hangfire;
using Hangfire.Mongo;
using Hangfire.Mongo.Migration.Strategies;
using Hangfire.Mongo.Migration.Strategies.Backup;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.PlatformAbstractions;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Swagger;
using Vat.Service.AdminApi.Extensions;
using Vat.Service.AdminApi.Extensions.Json.Converters;
using Vat.Service.AdminApi.MiddleWares;
using Vat.Service.AdminApi.Tasks;
using Vat.Service.Application.Adapter.AutoMapper.Profiles;
using Vat.Service.Application.Common.Http;
using Vat.Service.Application.Jobs;
using Vat.Service.Application.Jobs.Impl;
using Vat.Service.Application.Options;
using Vat.Service.Application.Services;
using Vat.Service.Application.Services.Impl;
using Vat.Service.Domain.Entity;
using Vat.Service.IoC.AutofacModules;
using Vat.Service.Repository.MongoDB;
using IHostingEnvironment = Microsoft.AspNetCore.Hosting.IHostingEnvironment;

namespace Vat.Service.AdminApi
{
    public class Startup
    {
        public Startup(IConfiguration configuration, IHostingEnvironment env)
        {
            IgnoreWatchJsonFile(configuration, env);
            Configuration = configuration;
            HostingEnvironment = env;
        }

        private void IgnoreWatchJsonFile(IConfiguration configuration, IHostingEnvironment env)
        {
            var builder = new ConfigurationBuilder()
            .SetBasePath(env.ContentRootPath)
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: false);
        }

        public IConfiguration Configuration { get; }
        public IHostingEnvironment HostingEnvironment { get; }

        public static bool IsInitComplete { get; set; } = false;

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddHealthChecks();

            services.AddCors(options =>
            {
                options.AddPolicy("AllowAll",
                    builder => builder.AllowAnyOrigin()
                        .AllowAnyMethod()
                        .AllowAnyHeader());
            });
            services.AddCustomAuthentication(Configuration);

            services.AddControllers()
                .AddNewtonsoftJson(x =>
                {
                    x.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
                    x.SerializerSettings.DateParseHandling = DateParseHandling.None;
                    //自定义序列化
                    var convers = new List<JsonConverter>();
                    convers.Add(new FormComponentConverter());
                    convers.Add(new BussinessConfigConverter());
                    convers.Add(new EPRProdcutConverter());
                    convers.Add(new DeclareMethodInfoConverter());
                    convers.Add(new TaxAgentRulesConverter());
                    convers.Add(new DeclarePayTaxDateEntityConverter());
                    x.SerializerSettings.Converters = convers;
                })
                .SetCompatibilityVersion(CompatibilityVersion.Latest);


            services.AddCustomNewtonsoftJsonConverter();
            //配置数据库连接, MQ
            services.Configure<MongoDBSettings>(Configuration.GetSection("MongoDBForWrite")); //默认读写
            services.Configure<MongoDBSettings>("MongoDBForWrite", Configuration.GetSection("MongoDBForWrite"));
            services.Configure<MongoDBSettings>("MongoDBForRead", Configuration.GetSection("MongoDBForRead"));
            services.Configure<RabbitMQEventBusSettings>(Configuration.GetSection("RabbitMQEventBus"));

            // services.AddHangfire(config =>
            // {
            //     // Read DefaultConnection string from appsettings.json
            //     var connectionString = Configuration.GetSection("HangfireMongoConnection:ConnectionString").Value;
            //     var hangfireDatabase = Configuration.GetSection("HangfireMongoConnection:Database").Value;
            //     var storageOptions = new MongoStorageOptions
            //     {
            //         MigrationOptions = new MongoMigrationOptions
            //         {
            //             MigrationStrategy = new MigrateMongoMigrationStrategy(),
            //             BackupStrategy = new CollectionMongoBackupStrategy()
            //         }
            //     };
            //     config.UseMongoStorage(connectionString, hangfireDatabase, storageOptions);
            // });

            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = Configuration.GetValue<string>("RedisConnectionString");
            });
            services.AddTokenGateway(config =>
            {
                config.GatewayUrl = Configuration["InnerGatewaySettings:ApiGatewayUrl"] + Configuration["InnerGatewaySettings:TokenService"];
            });


            services.Configure<AppSetting>(Configuration);
            services.Configure<ExchangeRateAddressSettings>(Configuration.GetSection("ExchangeRateAddressSettings"));
            services.AddCustomSwagger(Configuration);

            //配置Swagger
            services.ConfigureSwaggerGen(options =>
            {
                // UseFullTypeNameInSchemaIds replacement for .NET Core
                options.CustomSchemaIds(x => x.FullName);
            });

            //注册BsonClassMap
            MongoDBClassMap.Map();
            //添加AutoMapper的支持
            var ass = typeof(VatRegisterFile).Assembly;
            services.AddAutoMapper(ass);

            services.AddHttpClient();
            services.AddHttpClient<FileUploadClient>();
            services.AddHttpClient<AdminFileClient>();
            services.AddHttpClient<TenantService>();
            services.AddHttpClient<TenantUserClient>();
            services.AddHttpClient<WechatWebHookClient>();
            services.Configure<AppSettings>(Configuration);
            services.AddSingleton<IConverter>(new SynchronizedConverter(new PdfTools()));

            services.AddHttpClient<Service.Common.Http.FileClient>();
            services.Configure<Vat.Service.Common.Option.AppSettings>(Configuration);

            //改成BackgroudServeric
            //services.AddSingleton<BackgroundTask>();
            // services.AddHostedService<BackgroundTask>();

        }

        public void ConfigureContainer(ContainerBuilder builder)
        {
            builder.RegisterModule(new CommonModule());
            builder.RegisterModule(new RepositoryModule());
            builder.RegisterModule(new ApplicationModule());
            builder.RegisterModule(new MediatorModule());
            builder.RegisterType<AdminIdentityService>().As<IAdminIdentityService>().InstancePerLifetimeScope();
            builder.RegisterType<AmazonClient>().As<IAmazonClient>().InstancePerLifetimeScope();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostingEnvironment env)
        {

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseHsts();
            }

            app.Use(async (context, next) =>
            {
                context.Request.EnableBuffering();
                await next();
            });

            var pathBase = Configuration["PATH_BASE"];
            if (!string.IsNullOrEmpty(pathBase))
            {
                app.UsePathBase(pathBase);
            }

            app.UseMiddleware(typeof(ExceptionHandlerMiddleWare));
            app.UseStaticFiles(new StaticFileOptions()
            {
                FileProvider = new PhysicalFileProvider(
               Path.Combine(Directory.GetCurrentDirectory(), @"StaticFiles")),
                RequestPath = new PathString("/StaticFiles")
            });


            app.UseRouting();

            app.UseCors("AllowAll");
            // app.UseAuthentication();
            // app.UseAuthorization();

            //事件订阅（本地注释，代码不迁入）
            // app.EventBusConfig();

            //启动Swagger
            app.UseSwagger().UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint($"{(!string.IsNullOrEmpty(pathBase) ? pathBase : string.Empty)}/swagger/v1/swagger.json", "Vat Service API V1");
                // c.OAuthClientId("vatadminswaggerui");
                // c.OAuthAppName("Vat Service API Swagger UI");

            });
            //app.UseSwaggerUI(c => {
            //    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Vat Service API V1");
            //});
            #region 解决Ubuntu Nginx 代理不能获取IP问题
            // app.UseForwardedHeaders(new ForwardedHeadersOptions
            // {
            //     ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
            // });
            #endregion

            //Hangfire Init
            //启动Hangfire
            // var hangfireDashboardPath = "/vatAdminBackgroundService/hangfire";
            // if (env.IsDevelopment())
            // {
            //     hangfireDashboardPath = "/hangfire";
            // }

            // app.UseHangfireDashboard(hangfireDashboardPath, new DashboardOptions()
            // {
            //     Authorization = new[] { new HangfireDashboardAuthorizationFilter() }
            // });

            // var options = new BackgroundJobServerOptions
            // {
            //     ServerName = String.Format(
            //         "{0}.{1}",
            //         Environment.MachineName,
            //         Guid.NewGuid().ToString())
            // };

            // app.UseHangfireServer(options);

            //改成服务
            //var backgroundTask = app.ApplicationServices.GetService<BackgroundTask>();
            //backgroundTask.RegisterAsync().GetAwaiter().GetResult();




            app.UseAuditMiddleware(_ => _
            .FilterByRequest(rq => !rq.Path.Value.EndsWith("favicon.ico"))
            .IncludeRequestBody()
            );

            app.UseEndpoints(opts =>
            {
                opts.MapDefaultControllerRoute();
                // opts.MapHealthChecks("/health");
            });

            IsInitComplete = true;
        }
    }
}
