﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Vat.Service.Application.Jobs;
using Vat.Service.Application.Jobs.Impl;

namespace Vat.Service.AdminApi.Tasks
{
    public class BackgroundTask : BackgroundService
    {
        private readonly ITaskJobServiceService _taskJobService = null;
        public BackgroundTask(ITaskJobServiceService taskJobService)
        {
            _taskJobService = taskJobService;
        }

        public Task RegisterAsync()
        {

            //每月01号执行 统计所有需要发送申报提醒的客户
            //RecurringJob.AddOrUpdate("VatDeclareDatasStatisticsJob", () => _taskJobService.StaticticsTask(), Cron.Monthly(03));
            RecurringJob.RemoveIfExists("VatDeclareDatasStatisticsJob");

            ////英国VAT每月15号0点执行: 0 0 15 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("GBVatDeclareNotifyStatisticsJob", () => _taskJobService.GBSendNotification(), Cron.Monthly(15));
            RecurringJob.RemoveIfExists("GBVatDeclareNotifyStatisticsJob");

            ////德国意大利月度VAT申报   每月01号0点执行: 0 0 01 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("DEVatDeclareNotifyStatisticsJob", () => _taskJobService.DESendNotification(), Cron.Monthly(01));
            RecurringJob.RemoveIfExists("DEVatDeclareNotifyStatisticsJob");

            ////德国季度VAT申报   每1 4 7 10月 01号0点执行: 0 0 01 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("DEVatQuarterlyDeclareNotifyStatisticsJob", () => _taskJobService.DEQuarterlySendNotification(), "00 00 01 1,4,7,10 *");
            RecurringJob.RemoveIfExists("DEVatQuarterlyDeclareNotifyStatisticsJob");

            ////西班牙季度VAT申报    每1 4 7 10月 01号0点执行: 0 0 01 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("ESVatDeclareNotifyStatisticsJob", () => _taskJobService.ESSendNotification(), "00 00 01 1,4,7,10 *");
            RecurringJob.RemoveIfExists("ESVatDeclareNotifyStatisticsJob");

            ////意大利季度VAT申报     每1 4 7 10月 05号0点执行: 0 0 01 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("ITVatDeclareNotifyStatisticsJob", () => _taskJobService.ITSendNotification(), "00 00 05 1,4,7,10 *");
            RecurringJob.RemoveIfExists("ITVatDeclareNotifyStatisticsJob");

            //VAT亚马逊销售数据报表申请状态查询 每一小时执行一次
            //RecurringJob.AddOrUpdate("CheckAmazonVatSaleDatasReportRequestJob", () => _taskJobService.CheckAmazonVatSaleDatasReportRequest(), Cron.Hourly,TimeZoneInfo.Local);

            //VAT亚马逊销售数据报表下载 每一小时执行一次
            //RecurringJob.AddOrUpdate("DownLoadAmazonVatSaleDatasReportJob", () => _taskJobService.DownLoadAmazonVatSaleDatasReport(), Cron.Hourly(03), TimeZoneInfo.Local);

            //待申报数据定时清理  每天一次
            //RecurringJob.AddOrUpdate("VatDeclarePendingDataCleaningJob", () => _taskJobService.VatDeclarePendingDataCleaning(), Cron.Daily(17));
            RecurringJob.RemoveIfExists("VatDeclarePendingDataCleaningJob");

            //每月生成可准备申报事项
            //RecurringJob.AddOrUpdate("VatPreDeclareRemindJob", () => _taskJobService.VatPreDeclareRemind(), Cron.Monthly(01));
            RecurringJob.RemoveIfExists("VatPreDeclareRemindJob");
            //每月1号8点生成待申报并发送邮件提醒
            RecurringJob.AddOrUpdate("ToDeclaredCreate", () => _taskJobService.ToDelcareOrderGenerate(), "0 8 1 * *", TimeZoneInfo.Local);
            //每月1号9点检查并发送邮件提醒
            RecurringJob.AddOrUpdate("SendNotify", () => _taskJobService.ToDelcareNotify(), "0  9 1 * *", TimeZoneInfo.Local);

            //每周三检查,现有税号数据状况
            RecurringJob.AddOrUpdate("SendTaxStatisticNotify", () => _taskJobService.SendTaxStatisticNotify(), Cron.Weekly(DayOfWeek.Wednesday, 10), TimeZoneInfo.Local);
            //每月申报订单发送通知检查
            RecurringJob.AddOrUpdate("SendTaxDeclaredNotify", () => _taskJobService.SendTaxDeclaredNotify(), "30 8 1 * *", TimeZoneInfo.Local);
            //每月1号8点30生成IOSS待申报并发送邮件提醒
            RecurringJob.AddOrUpdate("ToDeclaredIOSSCreate", () => _taskJobService.ToDelcareIOSSOrderGenerate(), "30 8 1 * *", TimeZoneInfo.Local);
            #region 测试时使用 ,job间隔短
            //每月01号执行 统计所有需要发送申报提醒的客户

            //RecurringJob.AddOrUpdate("VatDeclareDatasStatisticsJob", () => _taskJobService.StaticticsTask(), Cron.MinuteInterval(5));
            RecurringJob.RemoveIfExists("VatDeclareDatasStatisticsJob");
            ////英国VAT每月15号0点执行: 0 0 15 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("GBVatDeclareNotifyStatisticsJob", () => _taskJobService.GBSendNotification(), Cron.MinuteInterval(7));
            RecurringJob.RemoveIfExists("GBVatDeclareNotifyStatisticsJob");
            ////德国意大利月度VAT申报   每月01号0点执行: 0 0 01 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("DEVatDeclareNotifyStatisticsJob", () => _taskJobService.DESendNotification(), Cron.MinuteInterval(7));
            RecurringJob.RemoveIfExists("DEVatDeclareNotifyStatisticsJob");
            ////德国季度VAT申报   每1 4 7 10月 01号0点执行: 0 0 01 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("DEVatQuarterlyDeclareNotifyStatisticsJob", () => _taskJobService.DEQuarterlySendNotification(), Cron.MinuteInterval(7));
            RecurringJob.RemoveIfExists("DEVatQuarterlyDeclareNotifyStatisticsJob");
            ////西班牙季度VAT申报    每1 4 7 10月 01号0点执行: 0 0 01 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("ESVatDeclareNotifyStatisticsJob", () => _taskJobService.ESSendNotification(), Cron.MinuteInterval(7));
            RecurringJob.RemoveIfExists("ESVatDeclareNotifyStatisticsJob");
            ////意大利季度VAT申报     每1 4 7 10月 05号0点执行: 0 0 01 * * 发送申报提醒，短信和邮件提醒
            //RecurringJob.AddOrUpdate("ITVatDeclareNotifyStatisticsJob", () => _taskJobService.ITSendNotification(), Cron.MinuteInterval(7));
            RecurringJob.RemoveIfExists("ITVatDeclareNotifyStatisticsJob");

            #endregion

            //修复待申报记录refno
            //RecurringJob.AddOrUpdate("VatDeclarePendingRepairJob", () => _taskJobService.VatDeclarePendingUpdateRefno(), Cron.Daily(17));
            RecurringJob.RemoveIfExists("VatDeclarePendingRepairJob");

            //RecurringJob.AddOrUpdate("VatDeclareDatasStatisticsJob", () => _taskJobService.StaticticsTask(), "0 0/10 * * * ? ");//测试使用
            RecurringJob.RemoveIfExists("VatDeclareDatasStatisticsJob");


            //RecurringJob.AddOrUpdate("UpdateDeTaxrate", () => _taskJobService.UpdateDeTaxrate(), "59 23 31 1 7", TimeZoneInfo.Local);


            #region 定时抓取汇率到维易系统
            RecurringJob.AddOrUpdate("GetPoundExchangeRateToSystem", () => _taskJobService.GetPoundExchangeRateToSystem(), "0 3 1 * *", TimeZoneInfo.Local);

            RecurringJob.AddOrUpdate("GetEuroExchangeRateToSystem", () => _taskJobService.GetEuroExchangeRateToSystem(), "0 3 1 * *", TimeZoneInfo.Local);

            RecurringJob.AddOrUpdate("GetPolandExchangeRateToSystem", () => _taskJobService.GetPolandExchangeRateToSystem(), "0 3 1 * *", TimeZoneInfo.Local);

            RecurringJob.AddOrUpdate("GetEuroExchangeRateByDayToSystem", () => _taskJobService.GetEuroExchangeRateByDayToSystem(), "0 3 1 * *", TimeZoneInfo.Local);

            RecurringJob.AddOrUpdate("GetCZKExchangeRateToSystem", () => _taskJobService.GetCZKExchangeRateToSystem(), "0 3 1 * *", TimeZoneInfo.Local);
            #endregion
            #region 续费提醒
            //每天生成待续费通知
            //RecurringJob.AddOrUpdate("GenerateRenewNotify", () => _taskJobService.GenerateRenewNotify(), "30 1 2 * *", TimeZoneInfo.Local);
            //每月申报订单发送通知检查
            //RecurringJob.AddOrUpdate("SendRenewNotify", () => _taskJobService.SendRenewNotify(), "30 10 2 * *", TimeZoneInfo.Local);

            #endregion

            #region 公海管理
            // 每小时执行一次公海自动归属任务
            RecurringJob.AddOrUpdate("AutoMoveToPublicPool", () => _taskJobService.AutoMoveToPublicPool(), Cron.Hourly, TimeZoneInfo.Local);
            #endregion

            RecurringJob.AddOrUpdate("GetCZDeclareFormToEmail", () => _taskJobService.GetCZDeclareFormToEmail(), "0 9 20 * *", TimeZoneInfo.Local);
            RecurringJob.AddOrUpdate("GetITDeclareFormToEmail", () => _taskJobService.GetITDeclareFormToEmail(), "0 9 14 * *", TimeZoneInfo.Local);
            return Task.CompletedTask;
        }

        protected async override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!Startup.IsInitComplete)
            {
                await Task.Delay(TimeSpan.FromSeconds(1));
            }
            await RegisterAsync();
        }
    }
}
