using System;
using System.IO;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using BFE.Framework.Infrastructure.Authorization.Exceptions;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using BFE.Framework.Infrastructure.Crosscutting.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Vat.Service.AdminApi.Extensions;
using Vat.Service.Application.Exceptions;
using Vat.Service.Infastructure.Exception;

namespace Vat.Service.AdminApi.MiddleWares
{
    /// <summary>
    /// 全局异常处理器
    /// </summary>
    public class ExceptionHandlerMiddleWare
    {
        /// <summary>
        /// 下一个中间件
        /// </summary>
        private readonly RequestDelegate _next;

        private readonly IJsonConverter _jsonConverter;

        private readonly ILogger<ExceptionHandlerMiddleWare> _logger;

        /// <summary>
        /// </summary>
        public ExceptionHandlerMiddleWare(RequestDelegate next, IJsonConverter jsonConverter, ILogger<ExceptionHandlerMiddleWare> logger)
        {
            this._next = next;
            _jsonConverter = jsonConverter;
            _logger = logger;
        }

        /// <summary>
        /// </summary>
        public async Task Invoke(HttpContext context)
        {
            try
            {
                await _next(context); //执行下一个中间件
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(context, ex);
            }
        }

        /// <summary>
        /// 处理异常信息
        /// </summary>
        private Task HandleExceptionAsync(HttpContext context, Exception ex)
        {
            //基于guid生成一个错误唯一标识
            var ticketId = Guid.NewGuid().ToString();
            var requestInfo = RequestToString(context.Request, ticketId);

            // 自定义异常
            if (ex is VatValidationError || ex is VatCommonException || ex is AuthValidationError)
            {
                var errorCode = ex is VatValidationError
                    ? ((VatValidationError)ex).ErrorCode :
                    ex is AuthValidationError
                    ? ((AuthValidationError)ex).ErrorCode
                    : ((VatCommonException)ex).ErrorCode;


                var errorMsg = ex is VatValidationError
                    ? ((VatValidationError)ex).CustomMessage :
                    ex is AuthValidationError
                    ? ((AuthValidationError)ex).CustomMessage
                    : ((VatCommonException)ex).CustomMessage;

                var metadata = new ErrorData(errorCode, errorMsg, context.Request.GetAbsoluteUri())
                {
                    TicketId = ticketId,
                };

                _logger.LogWarning(ex, requestInfo);

                var response = context.Response;
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                response.ContentType = "application/json;charset=utf-8";

                return context.Response.WriteAsync(_jsonConverter.SerializeObject(metadata));
            }
            //序列化异常
            else if (ex is System.Runtime.Serialization.SerializationException)
            {
                var metadata = new ErrorData(string.Empty, string.Format("序列化发生异常，可能提交的数据格式有误：{0}", ex.Message), context.Request.GetAbsoluteUri())
                {
                    TicketId = ticketId,
                };

                _logger.LogError(ex, requestInfo);

                var response = context.Response;
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                response.ContentType = "application/json;charset=utf-8";
                return context.Response.WriteAsync(_jsonConverter.SerializeObject(metadata));
            }
            //未处理异常
            else
            {
                var metadata = new ErrorData(string.Empty, string.Format("抱歉！发现系统异常，请您用此TicketId:{0}联系系统管理员，我们将以最快的速度为您解决此问题，谢谢！", ticketId), context.Request.GetAbsoluteUri())
                {
                    TicketId = ticketId,
                };

                _logger.LogError(ex, requestInfo);

                var response = context.Response;
                response.StatusCode = (int)HttpStatusCode.InternalServerError;
                response.ContentType = "application/json;charset=utf-8";
                return context.Response.WriteAsync(_jsonConverter.SerializeObject(metadata));
            }
            return null;
        }

        private static string RequestToString(HttpRequest request, string ticketId = "")
        {
            var message = new StringBuilder();

            if (!string.IsNullOrEmpty(ticketId))
            {
                message.AppendLine($"[TicketId]: {ticketId} ");
            }

            if (request.Method != null)
            {
                message.AppendLine($"[Method]: {request.Method} ");
            }

            message.AppendLine($"[RequestUri]: {request.GetAbsoluteUri()} ");

            //头部Header
            if (request.Headers != null)
            {
                message.AppendLine("[Header] Values: ");
                foreach (var headerItem in request.Headers)
                {
                    message.AppendLine($"--> [{headerItem.Key}]: {headerItem.Value} ");
                }
            }

            //Body
            if (request.Method.NotNullOrBlank() && !request.Method.EqualsIgnoreCase("GET"))
            {
                var bodyContent = GetRequestBody(request.HttpContext).GetAwaiter().GetResult();
                message.AppendLine("[Body]: ");
                message.AppendLine($"--> {bodyContent}");
            }

            return message.ToString();
        }

        internal static async Task<string> GetRequestBody(HttpContext httpContext)
        {
            var body = httpContext.Request.Body;
            if (body != null && body.CanRead)
            {
                using (var stream = new MemoryStream())
                {
                    if (body.CanSeek)
                    {
                        body.Seek(0, SeekOrigin.Begin);
                    }
                    await body.CopyToAsync(stream);
                    if (body.CanSeek)
                    {
                        body.Seek(0, SeekOrigin.Begin);
                    }
                    return Encoding.UTF8.GetString(stream.ToArray());
                }
            }
            return null;
        }
    }
}
