﻿using System.Linq;
using System.Reflection;
using Autofac;
using BFE.Framework.Infrastructure.Crosscutting.EventBus;
using BFE.Framework.Infrastructure.Crosscutting.Json;
using BFE.Framework.Infrastructure.Json.NewtonsoftJson;
using FluentValidation;
using Identity.Application.Services;
using Identity.Application.Services.Impl;
using Newtonsoft.Json;
using Vat.Service.Application.Commands.VatRegister.Validations;
using Vat.Service.Application.Common.Net;
using Vat.Service.Application.Infrastructure.Job;
using Vat.Service.Application.Infrastructure.Job.Hangfire;
using Vat.Service.Application.IntegrationEvents;
using Vat.Service.Application.Jobs;
using Vat.Service.Application.Jobs.ExchangeRateJobs;
using Vat.Service.Application.Jobs.Impl;
using Vat.Service.Application.Queries;
using Vat.Service.Application.Queries.AgentService;
using Vat.Service.Application.Queries.Crm;
using Vat.Service.Application.Queries.DynamicForm;
using Vat.Service.Application.Queries.EPROrder;
using Vat.Service.Application.Queries.Impl.EURepOrder;
using Vat.Service.Application.Queries.InvoiceInfo;
using Vat.Service.Application.Queries.IOSS;
using Vat.Service.Application.Queries.Order;
using Vat.Service.Application.Queries.OtherServiceOrder;
using Vat.Service.Application.Queries.Product;
using Vat.Service.Application.Queries.RefundOrder;
using Vat.Service.Application.Queries.Tax;
using Vat.Service.Application.Queries.TaxAgent;
using Vat.Service.Application.Queries.TaxRate;
using Vat.Service.Application.Queries.VatCountries;
using Vat.Service.Application.Queries.VatDataAsn;
using Vat.Service.Application.Queries.VatDeclare;
using Vat.Service.Application.Queries.VatDeclareFiles;
using Vat.Service.Application.Queries.VatDeclarePending;
using Vat.Service.Application.Queries.VatExchangeRate;
using Vat.Service.Application.Queries.VatLetterManagement;
using Vat.Service.Application.Queries.VatLog;
using Vat.Service.Application.Queries.VatNationalBusiness;
using Vat.Service.Application.Queries.VatOtherFunctions;
using Vat.Service.Application.Queries.VatRegister;
using Vat.Service.Application.Services;
using Vat.Service.Application.Services.Factory;
using Vat.Service.Application.Services.Impl;
using Vat.Service.Application.Services.Impl.CountryBusiness;
using Vat.Service.Application.Services.VatDeclareReminder;
using Vat.Service.Common.Services;
using Vat.Service.Common.Services.Impl;

namespace Vat.Service.IoC.AutofacModules
{
    /// <summary>
    /// 应用层相关的依赖注入配置
    /// </summary>
    public class ApplicationModule : Autofac.Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            // Register the Command's Validators (Validators based on FluentValidation library)
            var validator = typeof(VatRegisterApproveRejectedCommandValidator).GetTypeInfo().Assembly;
            builder
                .RegisterAssemblyTypes(validator)
                .Where(t => t.IsClosedTypeOf(typeof(IValidator<>)))
                .AsImplementedInterfaces();


            // Register the Integration Event Handler
            builder.RegisterAssemblyTypes(typeof(IEventCommon).GetTypeInfo().Assembly)
                .AsClosedTypesOf(typeof(IIntegrationEventHandler<>));

            // 注册Schedule
            builder.RegisterType<HangfireJobSchedule>().As<IJobSchedule>().InstancePerLifetimeScope();


            builder.RegisterAssemblyTypes(typeof(EuroExchangeRateJob).GetTypeInfo().Assembly)
                .AsClosedTypesOf(typeof(IJob<>))
               .AsImplementedInterfaces().InstancePerLifetimeScope();
            //Register the Job
            builder.RegisterType<TaskJobService>().As<ITaskJobServiceService>().InstancePerLifetimeScope();
            builder.RegisterType<EmailSender>().As<IEmailSender>().InstancePerLifetimeScope();
            builder.RegisterType<SmsSender>().As<ISmsSender>().InstancePerLifetimeScope();

            // Register the Queries
            //builder.RegisterType<CarrierQueries>().As<ICarrierQueries>().InstancePerLifetimeScope();

            // Register the Extend Service
            //builder.RegisterType<CountryService>().As<ICountryService>().SingleInstance();

            builder.RegisterType<VatRegisterQueries>().As<IVatRegisterQueries>().InstancePerLifetimeScope();
            builder.RegisterType<VatDeclareQueries>().As<IVatDeclareQueries>().InstancePerLifetimeScope();
            builder.RegisterType<VatDeclareFilesQueries>().As<IVatDeclareFilesQueries>().InstancePerLifetimeScope();
            //vat国家
            builder.RegisterType<CountriesInfoQueries>().As<ICountriesInfoQueries>().InstancePerLifetimeScope();

            builder.RegisterType<VatExchangeRateQueries>().As<IVatExchangeRateQueries>().InstancePerLifetimeScope();
            builder.RegisterType<VatDataAsnQueries>().As<IVatDataAsnQueries>().InstancePerLifetimeScope();
            builder.RegisterType<VatOtherFunctionsQueries>().As<IVatOtherFunctionsQueries>().InstancePerLifetimeScope();
            builder.RegisterType<VatDeclarePendingQueries>().As<IVatDeclarePendingQueries>().InstancePerLifetimeScope();
            builder.RegisterType<VatLogQueries>().As<IVatLogQueries>().InstancePerLifetimeScope();
            builder.RegisterType<VatLetterManagementQueries>().As<IVatLetterManagemantQueries>().InstancePerLifetimeScope();

            builder.RegisterType<VatNationalBusinessQueries>().As<IVatNationalBusinessQueries>().InstancePerLifetimeScope();
            builder.RegisterType<TaxRateQueries>().As<ITaxRateQueries>().InstancePerLifetimeScope();
            builder.RegisterType<DynamicFormQueries>().As<IDynamicFormQueries>().InstancePerLifetimeScope();
            builder.RegisterType<TaxAgentQueries>().As<ITaxAgentQueries>().InstancePerLifetimeScope();

            builder.RegisterType<HttpMethods>().As<IHttpMethods>().InstancePerLifetimeScope();

            builder.RegisterType<DynamicFormService>().As<IDynamicFormService>().SingleInstance();
            builder.RegisterType<OperatorLogService>().As<IOperatorLogService>().SingleInstance();
            builder.RegisterType<TaxNumberQueries>().As<ITaxNumberQueries>().InstancePerLifetimeScope();
            builder.RegisterType<OrderQueries>().As<IOrderQueries>().InstancePerLifetimeScope();
            builder.RegisterType<DeclaredOrderQueries>().As<IDeclaredOrderQueries>().InstancePerLifetimeScope();
            builder.RegisterType<AgentServiceTaxQueries>().As<IAgentServiceTaxQueries>().InstancePerLifetimeScope();
            builder.RegisterType<IOSSOrderQueries>().As<IIOSSOrderQueries>().InstancePerLifetimeScope();
            builder.RegisterType<IOSSDeclaredOrderQueries>().As<IIOSSDeclaredOrderQueries>().InstancePerLifetimeScope();

            builder.RegisterType<OtherServiceOrderQueries>().As<IOtherServiceOrderQueries>().InstancePerLifetimeScope();
            builder.RegisterType<OfflineDataQueries>().As<IOfflineDataQueries>().InstancePerLifetimeScope();
            builder.RegisterType<OrderService>().As<IOrderService>().InstancePerLifetimeScope();

            builder.RegisterType<PurchaseOrderQueries>().As<IPurchaseOrderQueries>().InstancePerLifetimeScope();
            builder.RegisterType<ProductQueries>().As<IProductQueries>().InstancePerLifetimeScope();
            builder.RegisterType<RefundOrderQueries>().As<IRefundOrderQueries>().InstancePerLifetimeScope();
            builder.RegisterType<InvoiceInfoQueries>().As<IInvoiceInfoQueries>().InstancePerLifetimeScope();
            builder.RegisterType<EURepOrderQueries>().As<IEURepOrderQueries>().InstancePerLifetimeScope();
            builder.RegisterType<EPROrderQueries>().As<IEPROrderQueries>().InstancePerLifetimeScope();

            builder.RegisterType<CrmQueries>().As<ICrmQueries>().InstancePerLifetimeScope();
            //builder.RegisterType<DeclaredCalculateService>().As<IDeclaredCalculateService>().InstancePerLifetimeScope();
            //builder.RegisterType<IOSSDeclaredCalculateService>().As<IIOSSDeclaredCalculateService>().InstancePerLifetimeScope();
            builder.RegisterType<Vat.Service.Common.Services.Impl.DeclaredCalculateService>().As<Vat.Service.Common.Services.IDeclaredCalculateService>().InstancePerLifetimeScope();
            builder.RegisterType<Vat.Service.Common.Services.Impl.IOSSDeclaredCalculateService>().As<Vat.Service.Common.Services.IIOSSDeclaredCalculateService>().InstancePerLifetimeScope();
            builder.RegisterType<Vat.Service.Common.Services.Impl.LogRemarkService>().As<Vat.Service.Common.Services.ILogRemarkService>().InstancePerLifetimeScope();
            builder.RegisterType<NewtonsoftJsonConverter>().As<IJsonConverter>()
               .UsingConstructor(typeof(JsonSerializerSettings)).SingleInstance();

            builder.RegisterType<GBCountryBusinessService>().AsSelf().InstancePerLifetimeScope();
            builder.RegisterType<TaxService>().As<ITaxService>().InstancePerLifetimeScope();
            //VAT发送消息重构-注入依赖
            builder.RegisterType<VatDeclareReminderFactory>().AsSelf().InstancePerLifetimeScope();

            var allTypes = typeof(IVatDeclareReminder).Assembly.GetExportedTypes().ToList();
            var implIVatDeclareReminderTypes = allTypes.Where(p => p.GetInterfaces().Contains(typeof(IVatDeclareReminder))).ToList();
            foreach (var type in implIVatDeclareReminderTypes)
            {
                builder.RegisterType(type).As<IVatDeclareReminder>().InstancePerLifetimeScope();
            }

            var implIVatDeclareReminderMessageTemplateBuilder = allTypes.Where(p => p.GetInterfaces().Contains(typeof(IVatDeclareReminderMessageTemplateBuilder)) && !p.IsInterface).ToList();
            foreach (var type in implIVatDeclareReminderMessageTemplateBuilder)
            {

                builder.RegisterType(type).As<IVatDeclareReminderMessageTemplateBuilder>().InstancePerLifetimeScope();
            }
            builder.RegisterType<ApplicationServiceFactory>().AsSelf().InstancePerLifetimeScope();
            builder.RegisterType<PurchaseOrderService>().As<IPurchaseOrderService>().SingleInstance();
            builder.RegisterType<ManagerOperatorLogService>().As<IManagerOperatorLogService>().SingleInstance();
            builder.RegisterType<PublicPoolService>().AsSelf().InstancePerLifetimeScope();
        }
    }
}
