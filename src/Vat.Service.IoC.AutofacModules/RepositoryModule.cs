﻿using Autofac;
using Autofac.Core;
using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Data.MongoDB;
using BFE.Framework.Infrastructure.Data.MongoDB.Impl;
using Microsoft.Extensions.Options;
using Vat.Service.Domain.Repositories.DynamicForm;
using Vat.Service.Domain.Repositories.Order;
using Vat.Service.Domain.Repositories.Tax;
using Vat.Service.Domain.Repositories.VATDataAsn;
using Vat.Service.Domain.Repositories.VatDeclare;
using Vat.Service.Domain.Repositories.VatDeclareFiles;
using Vat.Service.Domain.Repositories.VatDeclarePending;
using Vat.Service.Domain.Repositories.VatDeclareSalesData;
using Vat.Service.Domain.Repositories.VatExchangeRate;
using Vat.Service.Domain.Repositories.VatLetterManagement;
using Vat.Service.Domain.Repositories.VatLogRemark;
using Vat.Service.Domain.Repositories.NationalBusiness;
using Vat.Service.Domain.Repositories.VatNotification;
using Vat.Service.Domain.Repositories.VatOtherFunctions;
using Vat.Service.Domain.Repositories.VatPreDeclareRemind;
using Vat.Service.Domain.Repositories.VatRegister;
using Vat.Service.Repository.MongoDB;
using Vat.Service.Repository.MongoDB.Order;
using Vat.Service.Repository.MongoDB.Tax;
using Vat.Service.Repository.MongoDB.VATDataAsn;
using Vat.Service.Repository.MongoDB.VatDeclare;
using Vat.Service.Repository.MongoDB.VatDeclareFiles;
using Vat.Service.Repository.MongoDB.VatDeclareSalesData;
using Vat.Service.Repository.MongoDB.VatExchangeRate;
using Vat.Service.Repository.MongoDB.VatLetterManagement;
using Vat.Service.Repository.MongoDB.VatLogRemark;
using Vat.Service.Repository.MongoDB.NationalBusiness;
using Vat.Service.Repository.MongoDB.VATNotification;
using Vat.Service.Repository.MongoDB.VatPreDeclareRemind;
using Vat.Service.Repository.MongoDB.VatRegister;
using Vat.Service.Domain.Repositories.TaxAgent;
using Vat.Service.Repository.MongoDB.TaxAgent;
using Vat.Service.Domain.AggregatesModel.OrderAgg;
using Vat.Service.Domain.Repositories.OtherServiceOrder;
using Vat.Service.Repository.MongoDB.OtherServiceOrder;
using Vat_Service.Domain.Repositories;
using Vat.Service.Repository.MongoDB.OfflineData;
using Vat.Service.Domain.Repositories.OfflineData;
using Vat.Service.Repository.MongoDB.ExchangeRateLog;
using Vat.Service.Domain.Repositories.ExchangeRateLog;
using Vat.Service.Repository.MongoDB.IOSSOrder;
using Vat.Service.Domain.Repositories.IOSSOrder;
using Vat.Service.Repository.MongoDB.PurchaseOrder;
using Vat.Service.Domain.Repositories.PurchaseOrder;
using Vat.Service.Repository.MongoDB.Product;
using Vat.Service.Domain.Repositories.Prodcut;
using Vat.Service.Repository.MongoDB.RefundOrder;
using Vat.Service.Domain.Repositories.RefundOrder;
using Vat.Service.Domain.Repositories.InvoiceInfo;
using Vat.Service.Repository.MongoDB.InvoiceInfo;
using Vat.Service.Domain.Repositories.EURepresentative;
using Vat.Service.Repository.MongoDB.EURepresentative;
using Vat.Service.Repository.MongoDB.EPROrder;
using Vat.Service.Domain.Repositories.EPROrder;
using Vat.Service.Domain.Repositories;
using Vat.Service.Repository.MongoDB.CrmCustomer;

namespace Vat.Service.IoC.AutofacModules
{
    /// <summary>
    /// 仓储相关的依赖注入模块
    /// </summary>
    public class RepositoryModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterType<MongoDBManager>().SingleInstance();

            //配置MongoDB上下文， 针对IOptions<MongoDBSettings>做特殊处理

            //默认用于读写
            builder.RegisterType<MongoDBContext>()
                .As<IMongoDBContext, IDBContext>()
                .WithParameter(new ResolvedParameter(
                    (pi, ctx) => pi.ParameterType == typeof(IOptions<MongoDBSettings>),
                    (pi, ctx) => Options.Create(ctx.Resolve<IOptionsMonitor<MongoDBSettings>>().Get("MongoDBForWrite"))))
                .InstancePerLifetimeScope();
            //用于只读
            builder.RegisterType<MongoDBContext>()
                .Named<IMongoDBContext>("ReadOnly")
                .WithParameter(new ResolvedParameter(
                    (pi, ctx) => pi.ParameterType == typeof(IOptions<MongoDBSettings>),
                    (pi, ctx) => Options.Create(ctx.Resolve<IOptionsMonitor<MongoDBSettings>>().Get("MongoDBForRead"))))
                .InstancePerLifetimeScope();

            /*
             //* 配置领域仓储
             */
            builder.RegisterType<VatRegisterRepository>().As<IVatRegisterRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VatLogRemarkRepository>().As<IVatLogRemarkRepostiory>().InstancePerLifetimeScope();
            builder.RegisterType<VatDeclareRepository>().As<IVatDeclareRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VatDeclareFilesRepository>().As<IVatDeclareFilesRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VatExchangeRateRepository>().As<IVatExchangeRateRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VATExchangeRateByDayRepository>().As<IVatExchangeRateByDayRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VatNotificationRepository>().As<IVatNotificationRepository>().InstancePerLifetimeScope();
            builder.RegisterType<RegisterAsnDataRepository>().As<IRegisterAsnDataRepository>().InstancePerLifetimeScope();
            builder.RegisterType<DeclareAsnDataRepository>().As<IDeclareAsnDataRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VatOtherFunctionsRepository>().As<IVatOtherFunctionsRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VatDeclarePendingRepository>().As<IVatDeclarePendingRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VatDeclareSalesDataRepository>().As<IVatDeclareSalesDataRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VatPreDeclareRemindRepository>().As<IVatPreDeclareRemindRepository>().InstancePerLifetimeScope();
            builder.RegisterType<DynamicFormRepository>().As<IDynamicFormRepository>().InstancePerLifetimeScope();
            builder.RegisterType<TaxNumberRepository>().As<ITaxNumberRepository>().InstancePerLifetimeScope();
            builder.RegisterType<RegisterOrderRepository>().As<IRegisterOrderRepository>().InstancePerLifetimeScope();
            builder.RegisterType<DeclaredOrderRepository>().As<IDeclaredOrderRepository>().InstancePerLifetimeScope();
            builder.RegisterType<PaymentRepository>().As<IPaymentRepository>().InstancePerLifetimeScope();

            builder.RegisterType<NationalBusinessRepository>().As<INationalBusinessRepository>().InstancePerLifetimeScope();
            builder.RegisterType<TaxAgentRepository>().As<ITaxAgentRepository>().InstancePerLifetimeScope();

            builder.RegisterType<DynamicFormDataRepository>().As<IDynamicFormDataRepository>().InstancePerLifetimeScope();
            builder.RegisterType<BusinessFieldRepository>().As<IBusinessFieldRepository>().InstancePerLifetimeScope();
            builder.RegisterType<CompanyInfoRepository>().As<ICompanyInfoRepository>().InstancePerLifetimeScope();
            builder.RegisterType<TaxDeclareMonthRepository>().As<ITaxDeclareMonthRepository>().InstancePerLifetimeScope();
            builder.RegisterType<DeductionCodeRepository>().As<IDeductionCodeRepository>().InstancePerLifetimeScope();

            builder.RegisterType<VatLetterManagementRepository>().As<IVatLetterManagementRepository>().InstancePerLifetimeScope();
            builder.RegisterType<OtherServiceOrderRepository>().As<IOtherServiceOrderRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ConfigChangedLogRepository>().As<IConfigChangedLogRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ExchangeRateLogRepository>().As<IExchangeRateLogRepository>().InstancePerLifetimeScope();
            builder.RegisterType<TaxAgentTemplateRepository>().As<ITaxAgentTemplateRepository>().InstancePerLifetimeScope();
            builder.RegisterType<OfflineTaxRepository>().As<IOfflineTaxRepository>().InstancePerLifetimeScope();
            builder.RegisterType<IOSSRegisterOrderRepository>().As<IIOSSRegisterOrderRepository>().InstancePerLifetimeScope();
            builder.RegisterType<EuroExchangeRateByDayRepository>().As<IEuroExchangeRateByDayRepository>().InstancePerLifetimeScope();

            builder.RegisterType<IOSSDeclaredOrderRepository>().As<IIOSSDeclaredOrderRepository>().InstancePerLifetimeScope();

            builder.RegisterType<SpecialTaxRateInfoRepository>().As<ISpecialTaxRateInfoRepository>().InstancePerLifetimeScope();
            builder.RegisterType<PurchaseOrderRepository>().As<IPurchaseOrderRepository>().InstancePerLifetimeScope();
            builder.RegisterType<VatOperationLogRepository>().As<IVatOperationLogRepostiory>().InstancePerLifetimeScope();
            builder.RegisterType<ProductRepository>().As<IProductRepository>().InstancePerLifetimeScope();
            builder.RegisterType<RefundOrderRepository>().As<IRefundOrderRepository>().InstancePerLifetimeScope();
            builder.RegisterType<InvoiceInfoRepository>().As<IInvoiceInfoRepository>().InstancePerLifetimeScope();
            builder.RegisterType<TaxRenewNotifyRepository>().As<ITaxRenewNotifyRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ManagerOperationLogRepository>().As<IManagerOperationLogRepository>().InstancePerLifetimeScope();
            builder.RegisterType<DeclareCalcRecordRepository>().As<IDeclareCalcRecordRepository>().InstancePerLifetimeScope();
            builder.RegisterType<EURepOrderRepository>().As<IEURepOrderRepository>().InstancePerLifetimeScope();
            builder.RegisterType<RepresentativeTemplateRepository>().As<IRepresentativeTemplateRepository>().InstancePerLifetimeScope();
            builder.RegisterType<EPROrderRepository>().As<IEPROrderRepository>().InstancePerLifetimeScope();
            builder.RegisterType<EPRTemplateRepository>().As<IEPRTemplateRepository>().InstancePerLifetimeScope();
            builder.RegisterType<CrmCustomerRepository>().As<ICrmCustomerRepository>().InstancePerLifetimeScope();
            builder.RegisterType<CustomerRepository>().As<ICustomerRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ChannelRepository>().As<IChannelRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ProjectRepository>().As<IProjectRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ServiceAgreementRepository>().As<IServiceAgreementRepository>().InstancePerLifetimeScope();
            builder.RegisterType<CrmAgentRepository>().As<ICrmAgentRepository>().InstancePerLifetimeScope();
            builder.RegisterType<FollowupRepository>().As<IFollowupRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ProductOrderRepository>().As<IProductOrderRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ProductOrderHandoverRepository>().As<IProductOrderHandoverRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ProductOrderDeliveryRepository>().As<IProductOrderDeliveryRepository>().InstancePerLifetimeScope();
            builder.RegisterType<CrmPaymentRepository>().As<ICrmPaymentRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ApproverRepository>().As<IApproverRepository>().InstancePerLifetimeScope();
            builder.RegisterType<ExpenseRepository>().As<IExpenseRepository>().InstancePerLifetimeScope();
            builder.RegisterType<PublicPoolRuleRepository>().As<IPublicPoolRuleRepository>().InstancePerLifetimeScope();
            /*
             * 配置只读仓储
             * 若构造的参数是IMongoDBContext， 则解析Name为ReadOnly的
             */
            builder.RegisterType<VatRegisterReadOnlyRepository>().As<IVatRegisterReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatLogRemarkReadOnlyRepository>().As<IVatLogRemarkReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatDeclareReadOnlyRepository>().As<IVatDeclareReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatDeclareFilesReadOnlyRepository>().As<IVatDeclareFilesReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatExchangeRateReadOnlyRepository>().As<IVatExchangeRateReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatNotificationReadOnlyRepository>().As<IVatNotificationReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<RegisterAsnDataReadOnlyRepository>().As<IRegisterAsnDataReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<DeclareAsnDataReadOnlyRepository>().As<IDeclareAsnDataReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatOtherFunctionsReadOnlyRepository>().As<IVatOtherFunctionsReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatDeclarePendingReadOnlyRepository>().As<IVatDeclarePendingReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatDeclareSalesDataReadOnlyRepository>().As<IVatDeclareSalesDataReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatPreDeclareRemindReadOnlyRepository>().As<IVatPreDeclareRemindReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<TaxNumberReadOnlyRepository>().As<ITaxNumberReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<NationalBusinessReadOnlyRepository>().As<INationalBusinessReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<TaxAgentReadOnlyRepository>().As<ITaxAgentReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<RegisterOrderReadOnlyRepository>().As<IRegisterOrderReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<DeclaredOrderReadOnlyRepository>().As<IDeclaredOrderReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<CompanyInfoReadOnlyRepository>().As<ICompanyInfoReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<VatLetterManagementReadOnlyRepository>().As<IVatLetterManagementReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<OtherServiceOrderReadOnlyRepository>().As<IOtherServiceOrderReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<TaxDeclareMonthReadOnlyRepository>().As<ITaxDeclareMonthReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<IOSSDeclaredOrderReadOnlyRepository>().As<IIOSSDeclaredOrderReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<PurchaseOrderReadOnlyRepository>().As<IPurchaseOrderReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<ProductReadOnlyRepository>().As<IProductReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<RefundOrderReadOnlyRepository>().As<IRefundOrderReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<InvoiceInfoReadOnlyRepository>().As<IInvoiceInfoReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<IOSSRegisterOrderReadOnlyRepository>().As<IIOSSRegisterOrderReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();
            builder.RegisterType<TaxRenewNotifyReadOnlyRepository>().As<ITaxRenewNotifyReadOnlyRepository>()
                .WithParameter(ResolvedParameter.ForNamed<IMongoDBContext>("ReadOnly")).InstancePerLifetimeScope();

        }
    }
}
